// Azkar data collections
final List<Map<String, dynamic>> morningAzkar = [
  {
    'zekr': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَٰهَ إِلَّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
    'description': 'من قالها حين يصبح كتب الله له مائة حسنة',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَٰهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ',
    'description': 'سيد الاستغفار',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ',
    'description': 'من قالها في الصباح والمساء كان حقاً على الله أن يرضيه',
    'count': 3
  },
];

final List<Map<String, dynamic>> eveningAzkar = [
  {
    'zekr': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَٰهَ إِلَّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
    'description': 'من قالها حين يمسي كتب الله له مائة حسنة',
    'count': 1
  },
  {
    'zekr': 'أَعُوذُ بِكَلِمَاتِ اللَّهِ التَّامَّاتِ مِنْ شَرِّ مَا خَلَقَ',
    'description': 'من قالها ثلاث مرات حين يمسي لم تضره حمة تلك الليلة',
    'count': 3
  },
];

final List<Map<String, dynamic>> prayerAzkar = [
  {
    'zekr': 'سُبْحَانَ اللَّهِ',
    'description': 'تسبيح بعد الصلاة',
    'count': 33
  },
  {
    'zekr': 'الْحَمْدُ لِلَّهِ',
    'description': 'تحميد بعد الصلاة',
    'count': 33
  },
  {
    'zekr': 'اللَّهُ أَكْبَرُ',
    'description': 'تكبير بعد الصلاة',
    'count': 33
  },
  {
    'zekr': 'لَا إِلَٰهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
    'description': 'يقال بعد التسبيح والتحميد والتكبير',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ أَنْتَ السَّلَامُ، وَمِنْكَ السَّلَامُ، تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ',
    'description': 'يقال بعد السلام من الصلاة',
    'count': 1
  },
  {
    'zekr': 'أَسْتَغْفِرُ اللَّهَ (ثَلَاثًا) اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ',
    'description': 'يقال بعد السلام من الصلاة',
    'count': 1
  }
];

final List<Map<String, dynamic>> wudhuAzkar = [
  {
    'zekr': 'بِسْمِ اللَّهِ',
    'description': 'يقال عند بداية الوضوء',
    'count': 1
  },
  {
    'zekr': 'أَشْهَدُ أَنْ لَا إِلَٰهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، وَأَشْهَدُ أَنَّ مُحَمَّدًا عَبْدُهُ وَرَسُولُهُ',
    'description': 'من قالها بعد الوضوء فُتحت له أبواب الجنة الثمانية',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ اجْعَلْنِي مِنَ التَّوَّابِينَ وَاجْعَلْنِي مِنَ الْمُتَطَهِّرِينَ',
    'description': 'يقال بعد الفراغ من الوضوء',
    'count': 1
  },
  {
    'zekr': 'سُبْحَانَكَ اللَّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لَا إِلَٰهَ إِلَّا أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ',
    'description': 'يقال بعد الفراغ من الوضوء',
    'count': 1
  }
];

final List<Map<String, dynamic>> hajjAzkar = [
  {
    'zekr': 'لَبَّيْكَ اللَّهُمَّ لَبَّيْكَ، لَبَّيْكَ لَا شَرِيكَ لَكَ لَبَّيْكَ، إِنَّ الْحَمْدَ وَالنِّعْمَةَ لَكَ وَالْمُلْكَ، لَا شَرِيكَ لَكَ',
    'description': 'التلبية في الحج والعمرة',
    'count': 1
  },
  {
    'zekr': 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
    'description': 'دعاء الطواف',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ هَذَا حَرَمُكَ وَأَمْنُكَ فَحَرِّمْ لَحْمِي وَدَمِي وَشَعْرِي وَبَشَرِي عَلَى النَّارِ',
    'description': 'دعاء دخول الحرم',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ اجْعَلْهُ حَجًّا مَبْرُورًا وَذَنْبًا مَغْفُورًا وَسَعْيًا مَشْكُورًا',
    'description': 'دعاء الحج',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ إِنَّ هَذَا بَلَدُكَ الْحَرَامُ، وَأَمْنُكَ، وَحَرَمُكَ، فَحَرِّمْ لُحُومَنَا وَدِمَاءَنَا عَلَى النَّارِ',
    'description': 'دعاء في عرفة',
    'count': 1
  }
];

final List<Map<String, dynamic>> sleepAzkar = [
  {
    'zekr': 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
    'description': 'من قالها حين ينام كتب الله له حسنات',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ',
    'description': 'كان النبي ﷺ يقولها عند النوم',
    'count': 3
  },
];

final List<Map<String, dynamic>> wakeupAzkar = [
  {
    'zekr': 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
    'description': 'يقال عند الاستيقاظ من النوم',
    'count': 1
  },
  {
    'zekr': 'لَا إِلَٰهَ إِلَّا أَنْتَ سُبْحَانَكَ إِنِّي كُنْتُ مِنَ الظَّالِمِينَ',
    'description': 'دعاء الاستيقاظ من النوم',
    'count': 1
  },
];

final List<Map<String, dynamic>> foodAzkar = [
  {
    'zekr': 'بِسْمِ اللَّهِ',
    'description': 'قبل الطعام',
    'count': 1
  },
  {
    'zekr': 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنِي هَذَا، وَرَزَقَنِيهِ، مِنْ غَيْرِ حَوْلٍ مِنِّي وَلَا قُوَّةٍ',
    'description': 'بعد الطعام',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا، وَقِنَا عَذَابَ النَّارِ',
    'description': 'بعد الطعام',
    'count': 1
  }
];

final List<Map<String, dynamic>> travelAzkar = [
  {
    'zekr': 'اللَّهُ أَكْبَرُ، اللَّهُ أَكْبَرُ، اللَّهُ أَكْبَرُ، سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ، وَإِنَّا إِلَى رَبِّنَا لَمُنْقَلِبُونَ',
    'description': 'دعاء السفر',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَذَا الْبِرَّ وَالتَّقْوَى، وَمِنَ الْعَمَلِ مَا تَرْضَى',
    'description': 'دعاء السفر',
    'count': 1
  },
  {
    'zekr': 'آيِبُونَ، تَائِبُونَ، عَابِدُونَ، لِرَبِّنَا حَامِدُونَ',
    'description': 'الدعاء عند العودة من السفر',
    'count': 1
  }
];

final List<Map<String, dynamic>> homeAzkar = [
  {
    'zekr': 'بِسْمِ اللَّهِ وَلَجْنَا، وَبِسْمِ اللَّهِ خَرَجْنَا، وَعَلَى رَبِّنَا تَوَكَّلْنَا',
    'description': 'عند دخول المنزل',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ إِنِّي أَسْأَلُكَ خَيْرَ الْمَوْلِجِ وَخَيْرَ الْمَخْرَجِ، بِسْمِ اللَّهِ وَلَجْنَا، وَبِسْمِ اللَّهِ خَرَجْنَا، وَعَلَى اللَّهِ رَبِّنَا تَوَكَّلْنَا',
    'description': 'عند الخروج من المنزل',
    'count': 1
  }
];

final List<Map<String, dynamic>> marketAzkar = [
  {
    'zekr': 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ، يُحْيِي وَيُمِيتُ، وَهُوَ حَيٌّ لَا يَمُوتُ، بِيَدِهِ الْخَيْرُ، وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
    'description': 'دعاء دخول السوق',
    'count': 1
  }
];

final List<Map<String, dynamic>> studyAzkar = [
  {
    'zekr': 'رَبِّ زِدْنِي عِلْمًا',
    'description': 'دعاء طلب العلم',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ انْفَعْنِي بِمَا عَلَّمْتَنِي وَعَلِّمْنِي مَا يَنْفَعُنِي وَزِدْنِي عِلْمًا',
    'description': 'دعاء طلب العلم',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ إِنِّي أَسْتَوْدِعُكَ مَا قَرَأْتُ وَمَا حَفِظْتُ، فَرُدَّهُ عَلَيَّ عِنْدَ حَاجَتِي إِلَيْهِ، إِنَّكَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
    'description': 'دعاء المذاكرة والحفظ',
    'count': 1
  }
];

final List<Map<String, dynamic>> weatherAzkar = [
  {
    'zekr': 'اللَّهُمَّ صَيِّباً نَافِعاً',
    'description': 'دعاء نزول المطر',
    'count': 1
  },
  {
    'zekr': 'مُطِرْنَا بِفَضْلِ اللَّهِ وَرَحْمَتِهِ',
    'description': 'ما يقال بعد نزول المطر',
    'count': 1
  },
  {
    'zekr': 'سُبْحَانَ الَّذِي يُسَبِّحُ الرَّعْدُ بِحَمْدِهِ وَالْمَلَائِكَةُ مِنْ خِيفَتِهِ',
    'description': 'دعاء سماع الرعد',
    'count': 1
  }
];

final List<Map<String, dynamic>> tasbeehAzkar = [
  {
    'zekr': 'سُبْحَانَ اللَّهِ',
    'description': 'من قالها مائة مرة حُطت خطاياه وإن كانت مثل زبد البحر',
    'count': 100
  },
  {
    'zekr': 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
    'description': 'من قالها مائة مرة في يوم حُطت خطاياه وإن كانت مثل زبد البحر',
    'count': 100
  },
  {
    'zekr': 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ سُبْحَانَ اللَّهِ الْعَظِيمِ',
    'description': 'كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن',
    'count': 100
  },
  {
    'zekr': 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
    'description': 'من قالها في يوم مائة مرة كانت له عدل عشر رقاب، وكتبت له مائة حسنة، ومحيت عنه مائة سيئة',
    'count': 100
  },
  {
    'zekr': 'الْحَمْدُ لِلَّهِ',
    'description': 'تملأ الميزان',
    'count': 100
  },
  {
    'zekr': 'اللَّهُ أَكْبَرُ',
    'description': 'من التسبيح العظيم',
    'count': 100
  },
  {
    'zekr': 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
    'description': 'كنز من كنوز الجنة',
    'count': 100
  },
  {
    'zekr': 'أَسْتَغْفِرُ اللَّهَ',
    'description': 'من لزم الاستغفار جعل الله له من كل هم فرجاً ومن كل ضيق مخرجاً',
    'count': 100
  },
  {
    'zekr': 'سُبْحَانَ اللَّهِ وَالْحَمْدُ لِلَّهِ وَلَا إِلَهَ إِلَّا اللَّهُ وَاللَّهُ أَكْبَرُ',
    'description': 'أحب الكلام إلى الله',
    'count': 100
  }
];
