import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoritesProvider with ChangeNotifier {
  Set<String> _favorites = {};
  final String _prefsKey = 'favorites';

  FavoritesProvider() {
    _loadFavorites();
  }

  Set<String> get favorites => _favorites;

  bool isFavorite(String zekr) => _favorites.contains(zekr);

  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favList = prefs.getStringList(_prefsKey);
    if (favList != null) {
      _favorites = Set<String>.from(favList);
      notifyListeners();
    }
  }

  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_prefsKey, _favorites.toList());
  }

  void addFavorite(String zekr) {
    _favorites.add(zekr);
    _saveFavorites();
    notifyListeners();
  }

  void removeFavorite(String zekr) {
    _favorites.remove(zekr);
    _saveFavorites();
    notifyListeners();
  }
}
