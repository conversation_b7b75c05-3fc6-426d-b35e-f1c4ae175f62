import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/initializer.dart';
import '../data/azkar_data.dart' as azkar;
import '../data/extended_azkar.dart';
import '../data/prayers_data.dart'; // Add this import

class AppSettings with ChangeNotifier {
  late SharedPreferences _prefs;
  bool _initialized = false;

  // القيم الافتراضية
  static const defaultPrimaryColor = Color.fromARGB(255, 0, 102, 51);
  static const defaultSecondaryColor = Color.fromARGB(255, 0, 128, 64);
  static const defaultAccentColor = Color.fromARGB(255, 0, 153, 76);
  static const defaultBackgroundColor = Color(0xFFFFFFFF);

  Future<void> init() async {
    if (!_initialized) {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      _initialized = true;
      notifyListeners();
    }
  }

  AppSettings() {
    init();
  }

  bool _isDarkMode = false;
  double _fontSize = 18.0;
  TimeOfDay _morningAzkarTime = const TimeOfDay(hour: 7, minute: 0);
  TimeOfDay _eveningAzkarTime = const TimeOfDay(hour: 17, minute: 0);
  int _appColor = const Color.fromARGB(255, 95, 124, 138).value;
  bool _notificationSound = true;
  String _selectedSound = 'notification.mp3';
  bool _isInitialized = false;

  // إضافة متغيرات السبحة
  int _dhikrCount = 0;
  int _targetCount = 33;
  bool _vibrateOnComplete = true;

  bool get isDarkMode => _isDarkMode;
  double get fontSize => _fontSize;
  TimeOfDay get morningAzkarTime => _morningAzkarTime;
  TimeOfDay get eveningAzkarTime => _eveningAzkarTime;
  int get appColorValue => _appColor;
  bool get notificationSound => _notificationSound;
  String get selectedSound => _selectedSound;
  bool get isInitialized => _isInitialized;

  // إضافة getters
  int get dhikrCount => _dhikrCount;
  int get targetCount => _targetCount;
  bool get vibrateOnComplete => _vibrateOnComplete;

  Color get primaryColor =>
      Color(_prefs.getInt('appColor') ?? defaultPrimaryColor.value);
  Color get secondaryColor => Color(
      _prefs.getInt('customSecondaryColor') ?? defaultSecondaryColor.value);
  Color get accentColor =>
      Color(_prefs.getInt('customAccentColor') ?? defaultAccentColor.value);
  Color get backgroundColor => Color(
      _prefs.getInt('customBackgroundColor') ?? defaultBackgroundColor.value);
  Color get appColor =>
      Color(_prefs.getInt('appColor') ?? defaultPrimaryColor.value);

  Future<void> initialize() async {
    try {
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing settings: $e');
      await _resetToDefaults();
    } finally {
      notifyListeners();
    }
  }

  Future<void> _loadSettings() async {
    try {
      debugPrint('Loading settings from SharedPreferences...');

      // تحميل كل إعداد مع التحقق من صحة القيمة
      _isDarkMode = _prefs.getBool('isDarkMode') ?? false;
      debugPrint('Loaded isDarkMode: $_isDarkMode');

      _fontSize = _prefs.getDouble('fontSize') ?? 18.0;
      if (_fontSize < 14 || _fontSize > 30) _fontSize = 18.0;
      debugPrint('Loaded fontSize: $_fontSize');

      final morningHour = _prefs.getInt('morningAzkarHour') ?? 7;
      final morningMinute = _prefs.getInt('morningAzkarMinute') ?? 0;
      if (morningHour >= 0 &&
          morningHour < 24 &&
          morningMinute >= 0 &&
          morningMinute < 60) {
        _morningAzkarTime = TimeOfDay(hour: morningHour, minute: morningMinute);
      }
      debugPrint(
          'Loaded morningAzkarTime: ${_morningAzkarTime.hour}:${_morningAzkarTime.minute}');

      final eveningHour = _prefs.getInt('eveningAzkarHour') ?? 17;
      final eveningMinute = _prefs.getInt('eveningAzkarMinute') ?? 0;
      if (eveningHour >= 0 &&
          eveningHour < 24 &&
          eveningMinute >= 0 &&
          eveningMinute < 60) {
        _eveningAzkarTime = TimeOfDay(hour: eveningHour, minute: eveningMinute);
      }
      debugPrint(
          'Loaded eveningAzkarTime: ${_eveningAzkarTime.hour}:${_eveningAzkarTime.minute}');

      final colorValue = _prefs.getInt('appColor');
      if (colorValue != null) {
        _appColor = colorValue;
      }
      debugPrint('Loaded appColor: $_appColor');

      _notificationSound = _prefs.getBool('notificationSound') ?? true;
      debugPrint('Loaded notificationSound: $_notificationSound');

      _selectedSound = _prefs.getString('selectedSound') ?? 'notification.mp3';
      debugPrint('Loaded selectedSound: $_selectedSound');

      // تحميل إعدادات السبحة
      _dhikrCount = _prefs.getInt('lastDhikrCount') ?? 0; // تغيير اسم المفتاح
      _targetCount = _prefs.getInt('targetCount') ?? 33;
      _vibrateOnComplete = _prefs.getBool('vibrateOnComplete') ?? true;

      notifyListeners();
      debugPrint('All settings loaded successfully');
    } catch (e) {
      debugPrint('Error loading settings: $e');
      await _resetToDefaults();
      notifyListeners();
    }
  }

  Future<void> _resetToDefaults() async {
    debugPrint('Resetting settings to defaults...');

    _isDarkMode = false;
    _fontSize = 18.0;
    _morningAzkarTime = const TimeOfDay(hour: 7, minute: 0);
    _eveningAzkarTime = const TimeOfDay(hour: 17, minute: 0);
    _appColor = Colors.green.value;
    _notificationSound = true;
    _selectedSound = 'notification.mp3';
    _dhikrCount = 0;
    _targetCount = 33;
    _vibrateOnComplete = true;

    try {
      // حفظ القيم الافتراضية في SharedPreferences
      await Future.wait([
        _saveSetting('isDarkMode', _isDarkMode),
        _saveSetting('fontSize', _fontSize),
        _saveSetting('morningAzkarHour', _morningAzkarTime.hour),
        _saveSetting('morningAzkarMinute', _morningAzkarTime.minute),
        _saveSetting('eveningAzkarHour', _eveningAzkarTime.hour),
        _saveSetting('eveningAzkarMinute', _eveningAzkarTime.minute),
        _saveSetting('appColor', _appColor),
        _saveSetting('notificationSound', _notificationSound),
        _saveSetting('selectedSound', _selectedSound),
        _saveSetting('dhikrCount', _dhikrCount),
        _saveSetting('targetCount', _targetCount),
        _saveSetting('vibrateOnComplete', _vibrateOnComplete),
      ]);
      debugPrint('Default settings saved successfully');
    } catch (e) {
      debugPrint('Error saving default settings: $e');
    }
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    try {
      bool success = false;
      if (value is bool) {
        success = await _prefs.setBool(key, value);
      } else if (value is double) {
        success = await _prefs.setDouble(key, value);
      } else if (value is int) {
        success = await _prefs.setInt(key, value);
      } else if (value is String) {
        success = await _prefs.setString(key, value);
      }

      if (!success) {
        debugPrint('Failed to save setting $key');
        throw Exception('Failed to save setting $key');
      }

      debugPrint('Successfully saved setting $key: $value');
    } catch (e) {
      debugPrint('Error saving setting $key: $e');
      rethrow;
    }
  }

  Future<void> setDarkMode(bool value) async {
    try {
      await _saveSetting('isDarkMode', value);
      _isDarkMode = value;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting dark mode: $e');
    }
  }

  Future<void> setFontSize(double value) async {
    try {
      await _saveSetting('fontSize', value);
      _fontSize = value;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting font size: $e');
    }
  }

  Future<void> setMorningAzkarTime(TimeOfDay time) async {
    try {
      await _saveSetting('morningAzkarHour', time.hour);
      await _saveSetting('morningAzkarMinute', time.minute);
      _morningAzkarTime = time;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting morning azkar time: $e');
    }
  }

  Future<void> setEveningAzkarTime(TimeOfDay time) async {
    try {
      await _saveSetting('eveningAzkarHour', time.hour);
      await _saveSetting('eveningAzkarMinute', time.minute);
      _eveningAzkarTime = time;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting evening azkar time: $e');
    }
  }

  Future<void> setAppColor(Color color) async {
    try {
      await _prefs.setInt('appColor', color.value);
      _appColor = color.value;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting app color: $e');
      rethrow;
    }
  }

  Future<void> setCustomColors({
    Color? secondary,
    Color? accent,
    Color? background,
  }) async {
    try {
      if (secondary != null) {
        await _prefs.setInt('customSecondaryColor', secondary.value);
      }
      if (accent != null) {
        await _prefs.setInt('customAccentColor', accent.value);
      }
      if (background != null) {
        await _prefs.setInt('customBackgroundColor', background.value);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting custom colors: $e');
      rethrow;
    }
  }

  Future<void> setNotificationSound(bool value) async {
    try {
      await _saveSetting('notificationSound', value);
      _notificationSound = value;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting notification sound: $e');
    }
  }

  Future<void> setSelectedSound(String soundFile) async {
    try {
      await _saveSetting('selectedSound', soundFile);
      _selectedSound = soundFile;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting selected sound: $e');
    }
  }

  // دوال السبحة الإلكترونية
  Future<void> incrementDhikr() async {
    try {
      _dhikrCount++;
      await _saveSetting('lastDhikrCount', _dhikrCount); // تغيير اسم المفتاح
      notifyListeners();
    } catch (e) {
      debugPrint('Error incrementing dhikr: $e');
    }
  }

  Future<void> resetDhikr() async {
    try {
      _dhikrCount = 0;
      await _saveSetting('lastDhikrCount', _dhikrCount); // تغيير اسم المفتاح
      notifyListeners();
    } catch (e) {
      debugPrint('Error resetting dhikr: $e');
    }
  }

  Future<void> setTargetCount(int value) async {
    try {
      if (value > 0) {
        _targetCount = value;
        await _saveSetting('targetCount', _targetCount);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error setting target count: $e');
    }
  }

  Future<void> setVibrateOnComplete(bool value) async {
    try {
      _vibrateOnComplete = value;
      await _saveSetting('vibrateOnComplete', _vibrateOnComplete);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting vibrate on complete: $e');
    }
  }

  Future<void> setPrimaryColor(Color color) async {
    try {
      await _prefs.setInt('primaryColor', color.value);
      await _prefs.setInt('secondaryColor', color.withOpacity(0.8).value);
      await _prefs.setInt('accentColor', color.withOpacity(0.6).value);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting theme colors: $e');
      rethrow;
    }
  }

  Future<void> setSecondaryColor(Color color) async {
    try {
      await _prefs.setInt('secondaryColor', color.value);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting secondary color: $e');
    }
  }

  Future<void> setAccentColor(Color color) async {
    try {
      await _prefs.setInt('accentColor', color.value);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting accent color: $e');
    }
  }

  Future<void> setBackgroundColor(Color color) async {
    try {
      await _prefs.setInt('backgroundColor', color.value);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting background color: $e');
    }
  }

  List<Map<String, dynamic>> _getFavoriteAzkar(Set<String> favorites) {
    List<Map<String, dynamic>> allAzkar = [];

    void addAzkarFromCategory(List<Map<String, dynamic>> category) {
      for (var zekr in category) {
        if (favorites.contains(zekr['zekr'])) {
          allAzkar.add({...zekr});
        }
      }
    }

    // إضافة الأذكار من كل الفئات
    addAzkarFromCategory(azkar.morningAzkar);
    addAzkarFromCategory(azkar.tasbeehAzkar);

    // إضافة الأذكار الجديدة
    addAzkarFromCategory(quranAzkar);
    addAzkarFromCategory(prayers); // Now prayers will be recognized

    return allAzkar;
  }
}
