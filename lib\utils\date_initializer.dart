import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:hijri/hijri_calendar.dart';

class DateInitializer {
  static bool _initialized = false;

  static Future<void> initialize() async {
    if (!_initialized) {
      try {
        await initializeDateFormatting('ar', null);
        await initializeDateFormatting('ar_SA', null);
        Intl.defaultLocale = 'ar';
        HijriCalendar.setLocal('ar');
        _initialized = true;
        debugPrint('Date formatting initialized successfully');
      } catch (e) {
        debugPrint('Error initializing date formatting: $e');
        rethrow;
      }
    }
  }

  static bool get isInitialized => _initialized;
}
