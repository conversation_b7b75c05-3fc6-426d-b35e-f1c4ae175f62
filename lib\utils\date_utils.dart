import 'package:hijri/hijri_calendar.dart';

class ArabicDateUtils {
  static const List<String> arabicMonths = [
    'يناير',
    'فبراير',
    'مارس',
    'إبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمب<PERSON>',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر'
  ];

  static const List<String> hijriMonths = [
    'محرم',
    'صفر',
    'ربيع الأول',
    'ربيع الثاني',
    'جمادى الأولى',
    'جمادى الآخرة',
    'رجب',
    'شعبان',
    'رمضان',
    'شوال',
    'ذو القعدة',
    'ذو الحجة'
  ];

  static String toArabicNumbers(dynamic number) {
    if (number == null) return '';
    
    String str = number.toString();
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    for (int i = 0; i < english.length; i++) {
      str = str.replaceAll(english[i], arabic[i]);
    }
    
    return str;
  }

  static String formatArabicTime(DateTime time) {
    int hour = time.hour;
    final String period = hour < 12 ? 'صباحاً' : 'مساءً';
    
    // تحويل إلى نظام 12 ساعة
    if (hour > 12) {
      hour -= 12;
    } else if (hour == 0) {
      hour = 12;
    }
    
    final minute = time.minute.toString().padLeft(2, '0');
    return '${toArabicNumbers(hour)}:${toArabicNumbers(minute)} $period';
  }

  static String formatArabicDate(DateTime date) {
    // التاريخ الميلادي
    final day = toArabicNumbers(date.day);
    final month = arabicMonths[date.month - 1];
    final year = toArabicNumbers(date.year);
    
    // التاريخ الهجري
    final hijri = HijriCalendar.fromDate(date);
    final hijriDay = toArabicNumbers(hijri.hDay);
    final hijriMonth = hijriMonths[hijri.hMonth - 1];
    final hijriYear = toArabicNumbers(hijri.hYear);
    
    return '$day $month $year\n$hijriDay $hijriMonth $hijriYear هـ';
  }
}
