import 'dart:async';
import 'dart:io' show TimeoutException;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
// import 'audio_player_widget.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:azkar_moslim/screens/surah_detail_screen.dart';
import 'package:azkar_moslim/data/surah_names.dart';
import 'package:azkar_moslim/data/quran_data.dart';
import 'package:azkar_moslim/screens/juz_screen.dart';

class QuranScreen extends StatefulWidget {
  const QuranScreen({Key? key}) : super(key: key);

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with TickerProviderStateMixin {
  final AudioPlayer _audioPlayer = AudioPlayer();
  late final TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  late final AnimationController _playPauseAnimationController;

  bool _isPlaying = false;
  bool _isLoading = false;
  bool _isPaused = false;
  bool _isDragging = false;
  bool _isVolumeUpPressed = false;
  bool _isVolumeDownPressed = false;

  int _currentSurah = 1;
  int _currentVerse = 1;
  int _lastReadSurah = 1;
  int _lastReadVerse = 1;
  int _manualProgressSurah = 1;
  int _manualProgressVerse = 1;
  bool _showTraditionalVerseNumbers = true;
  Set<String> _bookmarkedVerses = <String>{};

  String _selectedReader = 'ماهر المعيقلي';

  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<void>? _playerCompleteSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;

  static const Map<String, Map<String, String>> _quranReaders = {
    'ماهر المعيقلي': {
      'code': 'MaherAlMuaiqly128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕌',
    },
    'عبد الباسط عبد الصمد': {
      'code': 'Abdul_Basit_Murattal_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'سيد القراء',
      'icon': '📿',
    },
    'مشاري العفاسي': {
      'code': 'Alafasy_128kbps',
      'country': '🇰🇼 الكويت',
      'description': 'القارئ المبدع',
      'icon': '🎙️',
    },
    'سعد الغامدي': {
      'code': 'Ghamadi_40kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕋',
    },
    'أحمد العجمي': {
      'code': 'ahmed_ibn_ali_al_ajamy_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ المتميز',
      'icon': '✨',
    },
    'محمود خليل الحصري': {
      'code': 'Husary_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'شيخ المقرئين',
      'icon': '👑',
    },
    'محمد أيوب': {
      'code': 'Muhammad_Ayyoub_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🌙',
    },
    'عبد الرحمن السديس': {
      'code': 'Abdurrahmaan_As-Sudais_192kbps',
      'country': '🇸🇦السعودية',
      'description': 'رئيس الحرمين الشريفين',
      'icon': '🏛️',
    },
    'أبو بكر الشاطري': {
      'code': 'Abu_Bakr_Ash-Shaatree_128kbps',
      'country': '🇾🇪 اليمن',
      'description': 'القارئ الشاطري',
      'icon': '🎵',
    },
    'علي الحذيفي': {
      'code': 'Hudhaify_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم النبوي',
      'icon': '🌟',
    },
    'عبدالله عواد الجهني': {
      'code': 'Abdullaah_3awwaad_Al-Juhaynee_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ المجيد',
      'icon': '🎭',
    },
    'علي جابر': {
      'code': 'Ali_Jaber_64kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ المحترف',
      'icon': '🎪',
    },
    'فارس عباد': {
      'code': 'Fares_Abbad_64kbps',
      'country': '🇸🇦السعودية',
      'description': 'صوت مميز',
      'icon': '🎨',
    },
    'ناصر القطامي': {
      'code': 'Nasser_Alqatami_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ الشاب',
      'icon': '🌺',
    },
    'ياسر الدوسري': {
      'code': 'Yasser_Ad-Dussary_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'الصوت الذهبي',
      'icon': '🏆',
    },
    'سعود الشريم': {
      'code': 'Saood_ash-Shuraym_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '💎',
    },
    'محمد المحيسني': {
      'code': 'Muhammad_al_Mohisni_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ الشاب المبدع',
      'icon': '🌸',
    },
    'خالد الجليل': {
      'code': 'Khalid_Al-Jaleel_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🌻',
    },
    'بندر بليلة': {
      'code': 'Bandar_Baleela_64kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🎯',
    },
    'صلاح البدير': {
      'code': 'Salah_Al_Budair_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🔮',
    },
    'عبد الله بصفر': {
      'code': 'Abdullah_Basfar_192bps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ المتقن',
      'icon': '🎁',
    },
    'محمد البراك': {
      'code': 'Mohammad_al_Barrak_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'صوت مؤثر',
      'icon': '🎻',
    },
    'عماد زهير حافظ': {
      'code': 'Emad_Zuhair_Hafez_128kbps',
      'country': '🇸🇦السعودية',
      'description': 'القارئ الحافظ',
      'icon': '📖',
    },
    'محمد صديق المنشاوي': {
      'code': 'Mohammad_Siddeeq_AlMinshawi_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'صاحب الصوت الذهبي',
      'icon': '🏆',
    },
    'محمد رفعت': {
      'code': 'Mohammed_Refaat_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الحرمين',
      'icon': '🎭',
    },
    'أحمد العزب': {
      'code': 'Ahmed_Al_Azab_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المبدع',
      'icon': '🎆',
    },
    'علي حجاج السويسي': {
      'code': 'Ali_Hajjaj_Al_Suesy_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'من علماء الأزهر',
      'icon': '🎓',
    },
    'محمود علي البنا': {
      'code': 'Mahmoud_Ali_Al_Banna_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الإذاعة المصرية',
      'icon': '📻',
    },
    'أبو العينين شعيشع': {
      'code': 'Abu_AlAynayn_Shoesha_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الأزهر الشريف',
      'icon': '🕌',
    },
    'عبد الفتاح الطروبي': {
      'code': 'Abdul_Fattah_At_Tarouti_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ معتمد',
      'icon': '🌿',
    },
    'محمد عبد الحكيم': {
      'code': 'Mohammad_Abdul_Hakeem_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ القاهرة',
      'icon': '🏦',
    },
    'محمود خليل الحصري - رواية ورش': {
      'code': 'Husary_Warsh_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'رواية ورش عن نافع',
      'icon': '📜',
    },
    'هاني الرفاعي': {
      'code': 'Hani_Rifai_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المصري المعروف',
      'icon': '🇪🇬',
    },
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _playPauseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _initializeAudioPlayer();
    _loadPreferences();
  }

  void _initializeAudioPlayer() async {
    debugPrint("Initializing audio player...");
    try {
      await _audioPlayer.setAudioContext(
        AudioContext(
          iOS: AudioContextIOS(
            category: AVAudioSessionCategory.playback,
            options: {
              AVAudioSessionOptions.mixWithOthers,
              AVAudioSessionOptions.defaultToSpeaker,
              AVAudioSessionOptions.allowAirPlay,
            },
          ),
          android: AudioContextAndroid(
            isSpeakerphoneOn: false,
            stayAwake: true,
            contentType: AndroidContentType.music,
            usageType: AndroidUsageType.media,
            audioFocus: AndroidAudioFocus.gainTransient,
          ),
        ),
      );

      await _audioPlayer.setReleaseMode(ReleaseMode.release);
      _volume = await _audioPlayer.volume;
      await _audioPlayer.setVolume(_volume);

      _playerStateSubscription = _audioPlayer.onPlayerStateChanged.listen(
        (state) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _isPlaying = state == PlayerState.playing;
                _isPaused = state == PlayerState.paused;
              });

              if (state == PlayerState.playing) {
                _playPauseAnimationController.forward();
              } else if (state == PlayerState.paused ||
                  state == PlayerState.stopped) {
                _playPauseAnimationController.reverse();
              }
            }
          });
        },
        onError: (error) {
          debugPrint('Error in player state subscription: $error');
        },
      );

      _playerCompleteSubscription = _audioPlayer.onPlayerComplete.listen(
        (event) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _isPlaying = false;
                _isPaused = false;
                _position = _duration;
              });

              // تأخير قصير للتأكد من اكتمال الآية
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) {
                  _playNextVerse();
                }
              });
            }
          });
        },
        onError: (error) {
          debugPrint('Error in player complete subscription: $error');
          // محاولة إعادة تشغيل الآية في حالة الخطأ
          if (mounted) {
            _playVerseLocally(_currentSurah, _currentVerse);
          }
        },
      );

      _durationSubscription = _audioPlayer.onDurationChanged.listen(
        (duration) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _duration = duration;
              });
            }
          });
        },
        onError: (error) {
          debugPrint('Error in duration subscription: $error');
        },
      );

      _positionSubscription = _audioPlayer.onPositionChanged.listen(
        (position) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _position = position;
              });
            }
          });
        },
        onError: (error) {
          debugPrint('Error in position subscription: $error');
        },
      );
    } catch (e) {
      debugPrint('Error initializing audio player: $e');
      debugPrint('Audio player initialization failed.');
    }
  }

  Future<void> _playVerse(int surahNumber, int verseNumber) async {
    debugPrint("Playing verse: $surahNumber:$verseNumber");

    if (_currentSurah == surahNumber &&
        _currentVerse == verseNumber &&
        _isPlaying) {
      await _togglePlayPause();
      return;
    }

    await _stopPlaybackLocally();

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _isPlaying = true;
      _currentSurah = surahNumber;
      _currentVerse = verseNumber;
      _lastReadSurah = surahNumber;
      _lastReadVerse = verseNumber;
    });

    _savePreferences();

    try {
      await _playVerseLocally(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error playing verse: $e');
      if (mounted) {
        setState(() => _isPlaying = false);
        setState(() => _isLoading = false);
        _showErrorSnackBar('تعذر تشغيل الآية. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      if (mounted && _isLoading) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _playVerseLocally(int surahNumber, int verseNumber) async {
    debugPrint("Playing verse locally: $surahNumber:$verseNumber");

    if (!mounted) return;

    try {
      setState(() {
        _isPlaying = true;
        _isLoading = true;
      });

      await _audioPlayer.stop();

      final url = _getUrlForReader(_selectedReader, surahNumber, verseNumber);
      debugPrint("Trying to play from URL: $url");

      await _audioPlayer.setSourceUrl(url);
      await _audioPlayer
          .setReleaseMode(ReleaseMode.release); // تغيير لضمان إكمال الآية
      await _audioPlayer.setVolume(_volume);

      // محاولة تشغيل الصوت مع إضافة مراقبة لحالة التشغيل
      try {
        await _audioPlayer.play(UrlSource(url));

        // انتظار للتأكد من بدء التشغيل
        await Future.delayed(const Duration(seconds: 1));

        // التحقق من حالة التشغيل
        if (!_isPlaying) {
          throw Exception('Failed to start playback');
        }
      } catch (e) {
        debugPrint('Error during playback: $e');
        rethrow;
      }

      bool played = false;
      String? lastError;

      final s3 = surahNumber.toString().padLeft(3, '0');
      final v3 = verseNumber.toString().padLeft(3, '0');
      final readerCode =
          _quranReaders[_selectedReader]?['code'] ?? 'MaherAlMuaiqly128kbps';

      final sources = [
        'https://everyayah.com/data/$readerCode/$s3$v3.mp3',
        'https://server.mp3quran.net/$readerCode/$s3$v3.mp3',
        'https://cdn.islamic.network/quran/audio/128/$readerCode/$surahNumber/$verseNumber.mp3',
      ];

      for (final url in sources) {
        try {
          debugPrint('Trying to play locally: $url');

          await _audioPlayer.setSource(UrlSource(url));

          await _audioPlayer.resume().timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              throw TimeoutException(
                  'Audio playback timed out', const Duration(seconds: 15));
            },
          );

          played = true;

          if (mounted) {
            setState(() {
              _isPlaying = true;
              _isLoading = false;
            });
          }

          break;
        } catch (e) {
          debugPrint('Failed to play locally from $url: $e');
          lastError = e.toString();
          try {
            await _audioPlayer.stop();
          } catch (stopError) {
            debugPrint('Error stopping player: $stopError');
          }
          continue;
        }
      }

      if (!played) {
        throw Exception('Failed to play from any source: $lastError');
      }
    } catch (e) {
      debugPrint('Error playing verse locally: $e');
      if (mounted) {
        setState(() {
          _isPlaying = false;
          _isLoading = false;
        });
        _showErrorSnackBar('تعذر تشغيل الآية. يرجى المحاولة مرة أخرى.');
      }
      rethrow;
    }
  }

  String _getUrlForReader(String readerName, int surahNumber, int verseNumber) {
    final s3 = surahNumber.toString().padLeft(3, '0');
    final v3 = verseNumber.toString().padLeft(3, '0');
    final readerCode =
        _quranReaders[readerName]?['code'] ?? 'MaherAlMuaiqly128kbps';
    return 'https://everyayah.com/data/$readerCode/${s3}${v3}.mp3';
  }

  void _playNextVerse() {
    _playNextVerseLocally();
  }

  Future<void> _playNextVerseLocally() async {
    if (_isLoading) return;
    setState(() => _isLoading = true);

    try {
      await _stopPlaybackLocally();

      int nextSurah = _currentSurah;
      int nextVerse = _currentVerse + 1;
      final verseCount = _getVerseCount(nextSurah);

      if (nextVerse <= verseCount) {
        await _playVerse(nextSurah, nextVerse);
        if (mounted) {
          setState(() {
            _currentVerse = nextVerse;
          });
        }
      } else if (nextSurah < 114) {
        nextSurah++;
        nextVerse = 1;
        await _playVerse(nextSurah, nextVerse);
        if (mounted) {
          setState(() {
            _currentSurah = nextSurah;
            _currentVerse = nextVerse;
          });
        }
      } else {
        if (mounted) {
          _showErrorSnackBar('تم الوصول إلى نهاية المصحف');
        }
      }
    } catch (e) {
      debugPrint('Error playing next verse: $e');
      if (mounted) {
        _showErrorSnackBar('تعذر تشغيل الآية التالية');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _playPreviousVerse() {
    _playPreviousVerseLocally();
  }

  Future<void> _playPreviousVerseLocally() async {
    if (_isLoading) return;
    setState(() => _isLoading = true);

    try {
      await _stopPlaybackLocally();

      int prevSurah = _currentSurah;
      int prevVerse = _currentVerse - 1;

      if (prevVerse >= 1) {
        await _playVerse(prevSurah, prevVerse);
        if (mounted) {
          setState(() {
            _currentVerse = prevVerse;
          });
        }
      } else if (prevSurah > 1) {
        prevSurah--;
        prevVerse = _getVerseCount(prevSurah);
        await _playVerse(prevSurah, prevVerse);
        if (mounted) {
          setState(() {
            _currentSurah = prevSurah;
            _currentVerse = prevVerse;
          });
        }
      } else {
        if (mounted) {
          _showErrorSnackBar('أنت في بداية المصحف');
        }
      }
    } catch (e) {
      debugPrint('Error playing previous verse: $e');
      if (mounted) {
        _showErrorSnackBar('تعذر تشغيل الآية السابقة');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getArabicNumber(int number) {
    const arabicNumbers = ['٠', '٢', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      return arabicNumbers[int.parse(digit)];
    }).join();
  }

  Widget _buildVerseNumber(int verseNumber) {
    if (_showTraditionalVerseNumbers) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.shade300, width: 1),
        ),
        child: Text(
          '۝${_getArabicNumber(verseNumber)}۝',
          style: GoogleFonts.amiri(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.green.shade700,
          ),
        ),
      );
    } else {
      return Container(
        width: 35,
        height: 35,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [Colors.green.shade400, Colors.green.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.green.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            _getArabicNumber(verseNumber),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      );
    }
  }

  void _toggleBookmark(int surahNumber, int verseNumber) async {
    final key = '${surahNumber}_$verseNumber';
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      if (_bookmarkedVerses.contains(key)) {
        _bookmarkedVerses.remove(key);
      } else {
        _bookmarkedVerses.add(key);
      }
    });

    await prefs.setStringList('bookmarked_verses', _bookmarkedVerses.toList());

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _bookmarkedVerses.contains(key)
                ? 'تم حفظ الآية في العلامات المرجعية'
                : 'تم إزالة الآية من العلامات المرجعية',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor:
              _bookmarkedVerses.contains(key) ? Colors.green : Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }

  Future<void> _markReadingProgress(int surahNumber, int verseNumber) async {
    setState(() {
      _manualProgressSurah = surahNumber;
      _manualProgressVerse = verseNumber;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('manual_progress_surah', surahNumber);
    await prefs.setInt('manual_progress_verse', verseNumber);

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حفظ تقدمرك في سورة ${_getSurahName(surahNumber)} - الآية ${_getArabicNumber(verseNumber)}',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _clearReadingProgress() async {
    setState(() {
      _manualProgressSurah = 1;
      _manualProgressVerse = 1;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('manual_progress_surah');
    await prefs.remove('manual_progress_verse');

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'تم مسح تقدم القراءة',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _playEntireQuran() async {
    if (!mounted) return;

    final shouldPlay = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قراءة القرآن كاملاً'),
        content: const Text(
          'هل تريد بدء قراءة القرآن الكريم بالكامل من الفاتحة؟ '
          'سيتم تشغيل التلاوة بشكل مستمر حتى انتهاء القرآن.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('بدء القراءة'),
          ),
        ],
      ),
    );

    if (shouldPlay == true && mounted) {
      _playVerse(1, 1);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('بدأ تشغيل القرآن الكريم من الفاتحة'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _resumeReading() async {
    if (!mounted) return;

    if (_lastReadSurah == 1 && _lastReadVerse == 1) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لم تقم بحفظ أي تقدم في القراءة بعد'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    final shouldResume = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استئناف القراءة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('استئناف'),
          ),
        ],
      ),
    );

    if (shouldResume == true && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SurahDetailScreen(
            surahNumber: _lastReadSurah,
            surahName: _getSurahName(_lastReadSurah),
          ),
        ),
      );
    }
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_reader', _selectedReader);
    await prefs.setInt('last_read_surah', _currentSurah);
    await prefs.setInt('last_read_verse', _currentVerse);
    await prefs.setBool(
      'show_traditional_verse_numbers',
      _showTraditionalVerseNumbers,
    );
    await prefs.setStringList('bookmarked_verses', _bookmarkedVerses.toList());
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedReader = prefs.getString('selected_reader') ?? 'ماهر المعيقلي';
      _lastReadSurah = prefs.getInt('last_read_surah') ?? 1;
      _lastReadVerse = prefs.getInt('last_read_verse') ?? 1;
      _manualProgressSurah = prefs.getInt('manual_progress_surah') ?? 1;
      _manualProgressVerse = prefs.getInt('manual_progress_verse') ?? 1;
      _showTraditionalVerseNumbers =
          prefs.getBool('show_traditional_verse_numbers') ?? true;
      _bookmarkedVerses = Set<String>.from(
        prefs.getStringList('bookmarked_verses') ?? [],
      );
      _currentSurah = _lastReadSurah;
      _currentVerse = _lastReadVerse;
    });
    _scrollToLastRead();
  }

  void _scrollToLastRead() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients && _lastReadSurah > 1) {
        const itemExtent = 60.0;
        final offset = (_lastReadSurah - 1) * itemExtent;
        _scrollController.jumpTo(
          offset.clamp(0.0, _scrollController.position.maxScrollExtent),
        );
      }
    });
  }

  Future<void> _stopPlayback() async {
    debugPrint("Stopping playback...");
    await _stopPlaybackLocally();
  }

  Future<void> _stopPlaybackLocally() async {
    if (!mounted) return;

    try {
      await _audioPlayer.stop();

      if (mounted) {
        setState(() {
          _isPlaying = false;
          _isPaused = false;
          _isLoading = false;
          _position = Duration.zero;
          _duration = Duration.zero;
        });
      }
    } catch (e) {
      debugPrint('Error stopping playback locally: $e');
      if (mounted) {
        setState(() {
          _isPlaying = false;
          _isPaused = false;
          _isLoading = false;
        });
        _showErrorSnackBar('تعذر إيقاف التشغيل');
      }
    }
  }

  Future<void> _seek(Duration position) async {
    try {
      if (_duration.inSeconds > 0) {
        await _audioPlayer.seek(position);
        debugPrint('Seeking to position: ${position.inSeconds} seconds');
        if (mounted) {
          setState(() {
            _position = position;
          });
        }
      }
    } catch (e) {
      debugPrint('Error seeking: $e');
      if (mounted) {
        _showErrorSnackBar('تعذر تغيير موضع التشغيل');
      }
    }
  }

  Future<void> _seekToPercent(double percent) async {
    if (_duration.inSeconds > 0) {
      final position =
          Duration(seconds: (percent * _duration.inSeconds).round());
      await _seek(position);
    }
  }

  Future<void> _adjustVolume(bool increase) async {
    try {
      double currentVolume = await _audioPlayer.volume;
      double newVolume = increase
          ? (currentVolume + 0.1).clamp(0.0, 1.0)
          : (currentVolume - 0.1).clamp(0.0, 1.0);

      await _audioPlayer.setVolume(newVolume);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'مستوى الصوت: ${(newVolume * 100).round()} %',
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.green.shade700,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error adjusting volume: $e');
      if (mounted) {
        _showErrorSnackBar('تعذر تغيير مستوى الصوت');
      }
    }
  }

  Future<void> _togglePlayPause() async {
    if (_isLoading) return;
    await _togglePlayPauseLocally();
  }

  Future<void> _togglePlayPauseLocally() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
        if (mounted) {
          setState(() {
            _isPlaying = false;
            _isPaused = true;
          });
        }
      } else if (_isPaused) {
        await _audioPlayer.resume();
        if (mounted) {
          setState(() {
            _isPlaying = true;
            _isPaused = false;
          });
        }
      } else {
        await _playVerse(_currentSurah, _currentVerse);
        if (mounted) {
          setState(() {
            _isPlaying = true;
            _isPaused = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error toggling local play/pause: $e');
      if (mounted) {
        _showErrorSnackBar('حدث خطأ في تشغيل الصوت');
      }
    }
  }

  void _showReaderSelectionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: LinearGradient(
                colors: [Colors.green.shade50, Colors.white],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade700,
                    borderRadius: BorderRadius.circular(18),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.shade700.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.record_voice_over,
                        color: Colors.white,
                        size: 32,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'اختر القارئ',
                        style: GoogleFonts.amiri(
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  constraints: const BoxConstraints(maxHeight: 400),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _quranReaders.keys.length,
                    itemBuilder: (context, index) {
                      final readerName = _quranReaders.keys.elementAt(index);
                      final readerInfo = _quranReaders[readerName]!;
                      final isSelected = _selectedReader == readerName;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color:
                              isSelected ? Colors.green.shade100 : Colors.white,
                          border: Border.all(
                            color: isSelected
                                ? Colors.green.shade500
                                : Colors.grey.shade300,
                            width: isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          leading: Container(
                            width: 55,
                            height: 55,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: isSelected
                                    ? [
                                        Colors.green.shade400,
                                        Colors.green.shade700,
                                      ]
                                    : [
                                        Colors.grey.shade300,
                                        Colors.grey.shade500,
                                      ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: isSelected
                                      ? Colors.green.shade700.withOpacity(0.3)
                                      : Colors.grey.withOpacity(0.2),
                                  blurRadius: 6,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                readerInfo['icon']!,
                                style: const TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          title: Text(
                            readerName,
                            style: GoogleFonts.amiri(
                              fontSize: 20,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w600,
                              color: isSelected
                                  ? Colors.green.shade800
                                  : Colors.black87,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                readerInfo['description']!,
                                style: TextStyle(
                                  fontSize: 15,
                                  color: isSelected
                                      ? Colors.green.shade700
                                      : Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                readerInfo['country']!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: isSelected
                                      ? Colors.green.shade600
                                      : Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                          trailing: isSelected
                              ? Icon(
                                  Icons.check_circle,
                                  color: Colors.green.shade600,
                                  size: 32,
                                )
                              : null,
                          onTap: () {
                            setState(() {
                              _selectedReader = readerName;
                            });
                            _savePreferences();
                            Navigator.of(context).pop();

                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'تم اختيار القارئ: $readerName',
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                  backgroundColor: Colors.green.shade600,
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.green.shade300,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.info,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'القارئ الحالي: $_selectedReader',
                        style: GoogleFonts.amiri(
                          fontSize: 17,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getSurahName(int surahNumber) {
    if (surahNumber >= 1 && surahNumber <= 114) {
      return surahNames[surahNumber - 1];
    }
    return '';
  }

  int _getVerseCount(int surahNumber) {
    const verseCounts = [
      7,
      286,
      200,
      176,
      120,
      165,
      206,
      75,
      129,
      109,
      123,
      111,
      43,
      52,
      99,
      128,
      111,
      110,
      98,
      135,
      112,
      78,
      118,
      64,
      77,
      227,
      93,
      88,
      69,
      60,
      34,
      30,
      73,
      54,
      45,
      83,
      182,
      88,
      75,
      85,
      54,
      53,
      89,
      59,
      37,
      35,
      38,
      29,
      18,
      45,
      60,
      49,
      62,
      55,
      78,
      96,
      29,
      22,
      24,
      13,
      14,
      11,
      11,
      18,
      12,
      12,
      30,
      52,
      52,
      44,
      28,
      28,
      20,
      56,
      40,
      31,
      50,
      40,
      46,
      42,
      29,
      19,
      36,
      25,
      22,
      17,
      19,
      26,
      30,
      20,
      15,
      21,
      11,
      8,
      8,
      11,
      11,
      8,
      3,
      9,
      5,
      4,
      7,
      3,
      6,
      3,
      5,
      4,
      5,
      6
    ];

    if (surahNumber >= 1 && surahNumber <= verseCounts.length) {
      return verseCounts[surahNumber - 1];
    }

    return 100;
  }

  Widget _buildBookmarkIcon(int surahNumber) {
    final isBookmarked = _bookmarkedVerses.contains('${surahNumber}_1');
    return IconButton(
      icon: Icon(
        isBookmarked ? Icons.bookmark : Icons.bookmark_border,
        color: isBookmarked ? Colors.amber : null,
      ),
      onPressed: () {
        _toggleBookmark(surahNumber, 1);
      },
    );
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _playerCompleteSubscription?.cancel();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _audioPlayer.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    _playPauseAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'القرآن الكريم',
            style: GoogleFonts.amiri(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.green.shade700,
          foregroundColor: Colors.white,
          actions: [
            if (_isPlaying || _isPaused)
              IconButton(
                icon: const Icon(Icons.stop, color: Colors.red),
                onPressed: _stopPlayback,
                tooltip: 'إيقاف',
              ),
            IconButton(
              icon: const Icon(Icons.play_circle, color: Colors.white),
              onPressed: _playEntireQuran,
              tooltip: 'قراءة القرآن كاملاً',
            ),
            IconButton(
              icon: const Icon(Icons.restore, color: Colors.white),
              onPressed: _resumeReading,
              tooltip: 'استئناف القراءة',
            ),
            IconButton(
              icon: const Icon(Icons.record_voice_over, color: Colors.white),
              onPressed: _showReaderSelectionDialog,
              tooltip: 'اختر القارئ',
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            tabs: const [
              Tab(text: 'السور'),
              Tab(text: 'الأجزاء'),
            ],
          ),
        ),
        body: Stack(
          children: [
            TabBarView(
              controller: _tabController,
              children: [
                _buildSurahList(),
                _buildJuzList(),
              ],
            ),
            if (_isPlaying || _isPaused)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: _buildMiniPlayer(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSurahList() {
    return ListView.builder(
      controller: _scrollController,
      itemCount: surahNames.length,
      itemBuilder: (context, index) {
        final surahNumber = index + 1;
        final surahName = surahNames[index];
        final isLastRead = surahNumber == _lastReadSurah;
        final isPlaying = _isPlaying && _currentSurah == surahNumber;
        final isPaused = _isPaused && _currentSurah == surahNumber;
        final isBookmarked = _bookmarkedVerses.contains('${surahNumber}_1');

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: isPlaying ? 6 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isPlaying ? Colors.green.shade700 : Colors.grey.shade300,
              width: isPlaying ? 2 : 1,
            ),
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            leading: Stack(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.green.shade700,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.shade700.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      '$surahNumber',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                if (isBookmarked)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Colors.amber,
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.bookmark,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            title: Text(
              surahName,
              style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade900,
              ),
            ),
            subtitle: Text(
              'الآيات: ${_getVerseCount(surahNumber)}${isLastRead ? ' • آخر قراءة' : ''}',
              style: TextStyle(
                fontSize: 14,
                color:
                    isLastRead ? Colors.orange.shade700 : Colors.grey.shade700,
                fontWeight: isLastRead ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: isPlaying
                      ? const Icon(Icons.pause_circle_filled)
                      : const Icon(Icons.play_circle_fill),
                  color:
                      isPlaying ? Colors.green.shade700 : Colors.green.shade600,
                  iconSize: 36,
                  onPressed: () {
                    if (isPlaying || isPaused) {
                      _togglePlayPause();
                    } else {
                      _playVerse(surahNumber, 1);
                    }
                  },
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _lastReadSurah = surahNumber;
                _lastReadVerse = 1;
              });
              _savePreferences();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SurahDetailScreen(
                    surahNumber: surahNumber,
                    surahName: surahName,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildJuzList() {
    return ListView.builder(
      itemCount: juzData.length,
      itemBuilder: (context, index) {
        final juz = juzData[index];

        int totalVerses = 0;
        for (int surahNumber in juz.surahs) {
          totalVerses += _getVerseCount(surahNumber);
        }

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.green.shade700,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.shade700.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '${juz.number}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            title: Text(
              juz.nameAr,
              style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade900,
              ),
            ),
            subtitle: Text(
              'السور: ${juz.surahs.length} • الآيات: $totalVerses',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Colors.green,
              size: 28,
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => JuzScreen(juz: juz),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAudioPlayer() {
    return Container(
      height: 120.0,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.green[900],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // Progress bar with draggable thumb
          _buildProgressBar(),
          // Control buttons
          _buildControlButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onHorizontalDragStart: (details) {
          setState(() => _isDragging = true);
        },
        onHorizontalDragUpdate: (details) {
          if (_duration.inSeconds > 0) {
            final RenderBox box = context.findRenderObject() as RenderBox;
            final double width = box.size.width;
            final double position = details.localPosition.dx.clamp(0, width);
            final double percent = position / width;
            setState(() {
              _position = Duration(
                  milliseconds: (percent * _duration.inMilliseconds).round());
            });
          }
        },
        onHorizontalDragEnd: (details) async {
          if (_duration.inSeconds > 0) {
            await _seek(_position);
          }
          setState(() => _isDragging = false);
        },
        child: Stack(
          children: [
            // Progress bar background
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            // Progress indicator
            FractionallySizedBox(
              widthFactor: _duration.inSeconds > 0
                  ? _position.inSeconds / _duration.inSeconds
                  : 0,
              child: Container(
                height: 8,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.white, Colors.white.withOpacity(0.8)],
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
            // Drag handle
            Positioned(
              left: _duration.inSeconds > 0
                  ? (_position.inSeconds /
                      _duration.inSeconds *
                      (MediaQuery.of(context).size.width - 64))
                  : 0,
              top: -4,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          icon: const Icon(Icons.skip_previous),
          color: Colors.white,
          onPressed: _playPreviousVerse,
        ),
        IconButton(
          icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
          color: Colors.white,
          iconSize: 32,
          onPressed: _togglePlayPause,
        ),
        IconButton(
          icon: const Icon(Icons.skip_next),
          color: Colors.white,
          onPressed: _playNextVerse,
        ),
      ],
    );
  }

  Widget _buildMiniPlayer() {
    return GestureDetector(
      onTap: _showPlayerBottomSheet,
      child: Container(
        height: 120.0,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: Colors.green[900],
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Progress bar
            Expanded(
              child: GestureDetector(
                onHorizontalDragStart: (details) {
                  setState(() => _isDragging = true);
                },
                onHorizontalDragUpdate: (details) {
                  if (_duration.inSeconds > 0) {
                    final RenderBox box =
                        context.findRenderObject() as RenderBox;
                    final double width =
                        box.size.width - 32.0; // accounting for padding
                    final double position =
                        details.localPosition.dx.clamp(0, width);
                    final double percent = position / width;
                    setState(() {
                      _position = Duration(
                          seconds: (percent * _duration.inSeconds).round());
                    });
                  }
                },
                onHorizontalDragEnd: (details) async {
                  if (_duration.inSeconds > 0) {
                    await _seek(_position);
                  }
                  setState(() => _isDragging = false);
                },
                child: Stack(
                  children: [
                    // خلفية شريط التقدم
                    Container(
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    // مؤشر التقدم
                    FractionallySizedBox(
                      widthFactor: _duration.inSeconds > 0
                          ? _position.inSeconds / _duration.inSeconds
                          : 0,
                      child: Container(
                        height: 8,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white,
                              Colors.white.withOpacity(0.8)
                            ],
                            begin: Alignment.centerRight,
                            end: Alignment.centerLeft,
                          ),
                          borderRadius: BorderRadius.circular(4),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // نقطة السحب
                    Positioned(
                      left: _duration.inSeconds > 0
                          ? (_position.inSeconds /
                              _duration.inSeconds *
                              (MediaQuery.of(context).size.width - 64))
                          : 0,
                      top: -4,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onHorizontalDragUpdate: (details) {
                          if (_duration.inSeconds > 0) {
                            final RenderBox box =
                                context.findRenderObject() as RenderBox;
                            final double width = box.size.width;
                            final double position = details.localPosition.dx;
                            final double fraction = position / width;
                            final newPosition = Duration(
                              seconds: (fraction * _duration.inSeconds).round(),
                            );
                            _seek(newPosition);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            // Time and Title Row
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  // Current Position
                  Text(
                    '${_position.inMinutes}:${(_position.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                  // Title and Verse Info
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          _getSurahName(_currentSurah),
                          style: GoogleFonts.amiri(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        Text(
                          'الآية ${_getArabicNumber(_currentVerse)}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  // Duration
                  Text(
                    '${_duration.inMinutes}:${(_duration.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // Control Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // أزرار التحكم بالصوت مع تأثيرات حركية
                GestureDetector(
                  onTapDown: (_) => setState(() => _isVolumeDownPressed = true),
                  onTapUp: (_) => setState(() => _isVolumeDownPressed = false),
                  onTapCancel: () =>
                      setState(() => _isVolumeDownPressed = false),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 150),
                    decoration: BoxDecoration(
                      color: _isVolumeDownPressed
                          ? Colors.white.withOpacity(0.3)
                          : Colors.transparent,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.volume_down),
                      color: Colors.white,
                      onPressed: () => _adjustVolume(false),
                    ),
                  ),
                ),
                // زر الآية السابقة
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.skip_previous),
                    iconSize: 32,
                    color: Colors.white,
                    onPressed: _playPreviousVerse,
                  ),
                ),
                // زر التشغيل/الإيقاف المؤقت
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  decoration: BoxDecoration(
                    color: _isPlaying
                        ? Colors.white.withOpacity(0.3)
                        : Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      customBorder: const CircleBorder(),
                      onTap: _togglePlayPause,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: AnimatedIcon(
                          icon: AnimatedIcons.play_pause,
                          progress: _playPauseAnimationController,
                          size: 48,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
                // زر الآية التالية
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.skip_next),
                    iconSize: 32,
                    color: Colors.white,
                    onPressed: _playNextVerse,
                  ),
                ),
                // زر رفع الصوت
                GestureDetector(
                  onTapDown: (_) => setState(() => _isVolumeUpPressed = true),
                  onTapUp: (_) => setState(() => _isVolumeUpPressed = false),
                  onTapCancel: () => setState(() => _isVolumeUpPressed = false),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 150),
                    decoration: BoxDecoration(
                      color: _isVolumeUpPressed
                          ? Colors.white.withOpacity(0.3)
                          : Colors.transparent,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.volume_up),
                      color: Colors.white,
                      onPressed: () => _adjustVolume(true),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  void _showPlayerBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.4,
          maxChildSize: 0.9,
          builder: (_, controller) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 12),
                  Container(
                    width: 40,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    _getSurahName(_currentSurah),
                    style: GoogleFonts.amiri(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'الآية ${_getArabicNumber(_currentVerse)}',
                    style: const TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SingleChildScrollView(
                      controller: controller,
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            Text(
                              "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                              textAlign: TextAlign.center,
                              style: GoogleFonts.amiri(fontSize: 24, height: 2),
                            ),
                            const SizedBox(height: 30),
                            Slider(
                              value: _position.inSeconds.toDouble(),
                              min: 0,
                              max: _duration.inSeconds
                                  .toDouble()
                                  .clamp(1, double.infinity),
                              onChanged: (value) {
                                _seek(Duration(seconds: value.toInt()));
                              },
                              activeColor: Colors.green,
                              inactiveColor: Colors.green.shade100,
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${_position.inMinutes}:${(_position.inSeconds % 60).toString().padLeft(2, '0')}',
                                  ),
                                  Text(
                                    '${_duration.inMinutes}:${(_duration.inSeconds % 60).toString().padLeft(2, '0')}',
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                IconButton(
                                  icon:
                                      const Icon(Icons.skip_previous, size: 36),
                                  onPressed: _playPreviousVerse,
                                ),
                                IconButton(
                                  icon: AnimatedIcon(
                                    icon: AnimatedIcons.play_pause,
                                    progress: _playPauseAnimationController,
                                    size: 64,
                                    color: Colors.green.shade700,
                                  ),
                                  onPressed: _togglePlayPause,
                                ),
                                IconButton(
                                  icon: const Icon(Icons.skip_next, size: 36),
                                  onPressed: _playNextVerse,
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.stop),
                                  onPressed: _stopPlayback,
                                  tooltip: 'إيقاف',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.record_voice_over),
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _showReaderSelectionDialog();
                                  },
                                  tooltip: 'تغيير القارئ',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.repeat),
                                  onPressed: () {},
                                ),
                                _buildBookmarkIcon(_currentSurah),
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
