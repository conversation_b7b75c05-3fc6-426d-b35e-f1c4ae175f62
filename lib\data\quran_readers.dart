
const Map<String, Map<String, String>> quranReaders = {
  'ماهر المعيقلي': {
    'code': 'MaherAlMuaiqly128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المكي',
    'icon': '🕌',
  },
  'عبد الباسط عبد الصمد': {
    'code': '<PERSON>_<PERSON><PERSON>t_Murattal_192kbps',
    'country': '🇪🇬 مصر',
    'description': 'سيد القراء',
    'icon': '📿',
  },
  'مشاري العفاسي': {
    'code': 'Alafasy_128kbps',
    'country': '🇰🇼 الكويت',
    'description': 'القارئ المبدع',
    'icon': '🎙️',
  },
  'سعد الغامدي': {
    'code': 'Ghamadi_40kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المكي',
    'icon': '🕋',
  },
  'أحمد العجمي': {
    'code': 'ah<PERSON>_ibn_ali_al_ajamy_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ المتميز',
    'icon': '✨',
  },
  'محمود خليل الحصري': {
    'code': 'Husary_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'شيخ المقرئين',
    'icon': '👑',
  },
  'محمد أيوب': {
    'code': 'Muhammad_Ayyoub_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المدني',
    'icon': '🌙',
  },
  'عبد الرحمن السديس': {
    'code': 'Abdurrahmaan_As-Sudais_192kbps',
    'country': '🇸🇦 السعودية',
    'description': 'رئيس الحرمين الشريفين',
    'icon': '🏛️',
  },
  'أبو بكر الشاطري': {
    'code': 'Abu_Bakr_Ash-Shaatree_128kbps',
    'country': '🇾🇪 اليمن',
    'description': 'القارئ الشاطري',
    'icon': '🎵',
  },
  'علي الحذيفي': {
    'code': 'Hudhaify_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم النبوي',
    'icon': '🌟',
  },
  'عبدالله عواد الجهني': {
    'code': 'Abdullaah_3awwaad_Al-Juhaynee_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ المجيد',
    'icon': '🎭',
  },
  'علي جابر': {
    'code': 'Ali_Jaber_64kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ المحترف',
    'icon': '🎪',
  },
  'فارس عباد': {
    'code': 'Fares_Abbad_64kbps',
    'country': '🇸🇦 السعودية',
    'description': 'صوت مميز',
    'icon': '🎨',
  },
  'ناصر القطامي': {
    'code': 'Nasser_Alqatami_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ الشاب',
    'icon': '🌺',
  },
  'ياسر الدوسري': {
    'code': 'Yasser_Ad-Dussary_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'الصوت الذهبي',
    'icon': '🏆',
  },
  'سعود الشريم': {
    'code': 'Saood_ash-Shuraym_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المكي',
    'icon': '💎',
  },
  'محمد المحيسني': {
    'code': 'Muhammad_al_Mohisni_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ الشاب المبدع',
    'icon': '🌸',
  },
  'خالد الجليل': {
    'code': 'Khalid_Al-Jaleel_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المكي',
    'icon': '🌻',
  },
  'بندر بليلة': {
    'code': 'Bandar_Baleela_64kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المدني',
    'icon': '🎯',
  },
  'صلاح البدير': {
    'code': 'Salah_Al_Budair_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'إمام الحرم المدني',
    'icon': '🔮',
  },
  'عبد الله بصفر': {
    'code': 'Abdullah_Basfar_192kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ المتقن',
    'icon': '🎁',
  },
  'محمد البراك': {
    'code': 'Mohammad_al_Barrak_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'صوت مؤثر',
    'icon': '🎻',
  },
  'عماد زهير حافظ': {
    'code': 'Emad_Zuhair_Hafez_128kbps',
    'country': '🇸🇦 السعودية',
    'description': 'القارئ الحافظ',
    'icon': '📖',
  },
  // قراء مصريون مشاهير
  'محمد صديق المنشاوي': {
    'code': 'Mohammad_Siddeeq_AlMinshawi_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'صاحب الصوت الذهبي',
    'icon': '🏆',
  },
  'محمد رفعت': {
    'code': 'Mohammed_Refaat_64kbps',
    'country': '🇪🇬 مصر',
    'description': 'قارئ الحرمين',
    'icon': '🎭',
  },
  'أحمد العزب': {
    'code': 'Ahmed_Al_Azab_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'القارئ المبدع',
    'icon': '🎆',
  },
  'علي حجاج السويسي': {
    'code': 'Ali_Hajjaj_Al_Suesy_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'من علماء الأزهر',
    'icon': '🎓',
  },
  'محمود علي البنا': {
    'code': 'Mahmoud_Ali_Al_Banna_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'قارئ الإذاعة المصرية',
    'icon': '📻',
  },
  'أبو العينين شعيشع': {
    'code': 'Abu_AlAynayn_Shoesha_64kbps',
    'country': '🇪🇬 مصر',
    'description': 'قارئ الأزهر الشريف',
    'icon': '🕌',
  },
  'عبد الفتاح الطروبي': {
    'code': 'Abdul_Fattah_At_Tarouti_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'قارئ معتمد',
    'icon': '🌿',
  },
  'محمد عبد الحكيم': {
    'code': 'Mohammad_Abdul_Hakeem_128kbps',
    'country': '🇪🇬 مصر',
    'description': 'قارئ القاهرة',
    'icon': '🏦',
  },
  // المزيد من القراء المصريين المشاهير
  'محمود خليل الحصري - رواية ورش': {
    'code': 'Husary_Warsh_64kbps',
    'country': '🇪🇬 مصر',
    'description': 'رواية ورش عن نافع',
    'icon': '📜',
  },
  'هاني الرفاعي': {
    'code': 'Hani_Rifai_192kbps',
    'country': '🇪🇬 مصر',
    'description': 'القارئ المصري المعروف',
    'icon': '🇪🇬',
  },
};
