// quran_data.dart
// ملاحظة: هذا الملف يحتوي على نموذج البيانات + جزء تمهيدي من المحتوى (الفاتحة كاملة).
// حجم المصحف كاملًا (6236 آية) كبير جدًا لوضعه في رسالة واحدة، لذلك جهزت الهيكل كاملاً
// ويمكن إكمال بقية السور بنفس البنية (SurahData).

// ignore_for_file: constant_identifier_names

class SurahData {
  final int number;
  final String name;
  final String nameAr;
  final int ayahCount;
  final String type; // مكية أو مدنية
  final int juzNumber; // رقم الجزء (أول ظهور للسورة)
  final List<String> verses; // الآيات مُشكّلة مع الترقيم

  const SurahData({
    required this.number,
    required this.name,
    required this.nameAr,
    required this.ayahCount,
    required this.type,
    required this.juz<PERSON><PERSON>ber,
    required this.verses,
  });
}

class JuzData {
  final int number;
  final String nameAr;
  final List<int> surahs; // أرقام السور التي تقع ضمن هذا الجزء (كأرقام سور)
  final String start; // بداية الجزء (سورة/آية)
  final String end; // نهاية الجزء (سورة/آية)

  const JuzData({
    required this.number,
    required this.nameAr,
    required this.surahs,
    required this.start,
    required this.end,
  });
}

// بيانات الأجزاء (30 جزءًا) — الأسماء الشائعة
const List<JuzData> juzData = [
  JuzData(
    number: 1,
    nameAr: 'الجزء الأوّل',
    surahs: [1, 2],
    start: 'الفاتحة 1:1',
    end: 'البقرة 2:141',
  ),
  JuzData(
    number: 2,
    nameAr: 'سيقول',
    surahs: [2],
    start: 'البقرة 2:142',
    end: 'البقرة 2:252',
  ),
  JuzData(
    number: 3,
    nameAr: 'تلك الرسل',
    surahs: [2, 3],
    start: 'البقرة 2:253',
    end: 'آل عمران 3:92',
  ),
  JuzData(
    number: 4,
    nameAr: 'لن تنالوا',
    surahs: [3, 4],
    start: 'آل عمران 3:93',
    end: 'النساء 4:23',
  ),
  JuzData(
    number: 5,
    nameAr: 'والمحصنات',
    surahs: [4],
    start: 'النساء 4:24',
    end: 'النساء 4:147',
  ),
  JuzData(
    number: 6,
    nameAr: 'لا يحب الله',
    surahs: [4, 5],
    start: 'النساء 4:148',
    end: 'المائدة 5:81',
  ),
  JuzData(
    number: 7,
    nameAr: 'وإذا سمعوا',
    surahs: [5, 6],
    start: 'المائدة 5:82',
    end: 'الأنعام 6:110',
  ),
  JuzData(
    number: 8,
    nameAr: 'ولو أننا',
    surahs: [6, 7],
    start: 'الأنعام 6:111',
    end: 'الأعراف 7:87',
  ),
  JuzData(
    number: 9,
    nameAr: 'قال الملأ',
    surahs: [7, 8],
    start: 'الأعراف 7:88',
    end: 'الأنفال 8:40',
  ),
  JuzData(
    number: 10,
    nameAr: 'واعلموا',
    surahs: [8, 9],
    start: 'الأنفال 8:41',
    end: 'التوبة 9:92',
  ),
  JuzData(
    number: 11,
    nameAr: 'يعتذرون',
    surahs: [9, 10, 11],
    start: 'التوبة 9:93',
    end: 'هود 11:5',
  ),
  JuzData(
    number: 12,
    nameAr: 'وما من دابة',
    surahs: [11, 12],
    start: 'هود 11:6',
    end: 'يوسف 12:52',
  ),
  JuzData(
    number: 13,
    nameAr: 'وما أبرئ',
    surahs: [12, 13, 14, 15],
    start: 'يوسف 12:53',
    end: 'الحجر 15:1',
  ),
  JuzData(
    number: 14,
    nameAr: 'ربما',
    surahs: [15, 16, 17],
    start: 'الحجر 15:2',
    end: 'الإسراء 17:98',
  ),
  JuzData(
    number: 15,
    nameAr: 'سبحان الذي',
    surahs: [17, 18, 19, 20],
    start: 'الإسراء 17:99',
    end: 'طه 20:135',
  ),
  JuzData(
    number: 16,
    nameAr: 'قال ألم',
    surahs: [21, 22],
    start: 'الأنبياء 21:1',
    end: 'الحج 22:78',
  ),
  JuzData(
    number: 17,
    nameAr: 'اقترب للناس',
    surahs: [23, 24, 25],
    start: 'المؤمنون 23:1',
    end: 'الفرقان 25:20',
  ),
  JuzData(
    number: 18,
    nameAr: 'قد أفلح',
    surahs: [25, 26, 27],
    start: 'الفرقان 25:21',
    end: 'النمل 27:55',
  ),
  JuzData(
    number: 19,
    nameAr: 'وقال الذين',
    surahs: [27, 28, 29],
    start: 'النمل 27:56',
    end: 'العنكبوت 29:45',
  ),
  JuzData(
    number: 20,
    nameAr: 'أمن خلق',
    surahs: [29, 30, 31, 32, 33],
    start: 'العنكبوت 29:46',
    end: 'الأحزاب 33:30',
  ),
  JuzData(
    number: 21,
    nameAr: 'اتل ما أوحي',
    surahs: [33, 34, 35, 36],
    start: 'الأحزاب 33:31',
    end: 'يس 36:21',
  ),
  JuzData(
    number: 22,
    nameAr: 'ومن يقنت',
    surahs: [36, 37, 38, 39],
    start: 'يس 36:22',
    end: 'الزمر 39:31',
  ),
  JuzData(
    number: 23,
    nameAr: 'ومالي',
    surahs: [39, 40, 41],
    start: 'الزمر 39:32',
    end: 'فصلت 41:46',
  ),
  JuzData(
    number: 24,
    nameAr: 'فمن أظلم',
    surahs: [41, 42, 43, 44, 45],
    start: 'فصلت 41:47',
    end: 'الجاثية 45:37',
  ),
  JuzData(
    number: 25,
    nameAr: 'إليه يرد',
    surahs: [46, 47, 48, 49, 50, 51],
    start: 'الأحقاف 46:1',
    end: 'الذاريات 51:30',
  ),
  JuzData(
    number: 26,
    nameAr: 'حم تنزيل',
    surahs: [51, 52, 53, 54, 55, 56, 57],
    start: 'الذاريات 51:31',
    end: 'الحديد 57:29',
  ),
  JuzData(
    number: 27,
    nameAr: 'قال فما خطبكم',
    surahs: [58, 59, 60, 61, 62, 63, 64, 65, 66],
    start: 'المجادلة 58:1',
    end: 'التحريم 66:12',
  ),
  JuzData(
    number: 28,
    nameAr: 'قد سمع الله',
    surahs: [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77],
    start: 'الملك 67:1',
    end: 'المرسلات 77:50',
  ),
  JuzData(
    number: 29,
    nameAr: 'تبارك الذي',
    surahs: [
      78,
      79,
      80,
      81,
      82,
      83,
      84,
      85,
      86,
      87,
      88,
      89,
      90,
      91,
      92,
      93,
      94,
      95,
      96,
      97,
      98,
      99,
      100,
      101,
      102,
      103,
      104,
      105,
      106,
      107,
      108,
      109,
      110,
      111,
      112,
      113,
      114
    ],
    start: 'النبأ 78:1',
    end: 'الناس 114:6',
  ),
  JuzData(
    number: 30,
    nameAr: 'عمَّ يتساءلون',
    surahs: [
      78,
      79,
      80,
      81,
      82,
      83,
      84,
      85,
      86,
      87,
      88,
      89,
      90,
      91,
      92,
      93,
      94,
      95,
      96,
      97,
      98,
      99,
      100,
      101,
      102,
      103,
      104,
      105,
      106,
      107,
      108,
      109,
      110,
      111,
      112,
      113,
      114
    ],
    start: 'النبأ 78:1',
    end: 'الناس 114:6',
  ),
];

// بيانات السور (تعريف تمهيدي + سورة الفاتحة كاملة كنموذج)
// يمكنك إكمال بقية السور بالصيغة نفسها.
const List<SurahData> quranData = [
  SurahData(
    number: 1,
    name: 'Al-Fatihah',
    nameAr: 'الفاتحة',
    ayahCount: 7,
    type: 'مكية',
    juzNumber: 1,
    verses: [
      'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ ﴿1﴾',
      'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿2﴾',
      'الرَّحْمَٰنِ الرَّحِيمِ ﴿3﴾',
      'مَالِكِ يَوْمِ الدِّينِ ﴿4﴾',
      'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ ﴿5﴾',
      'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ ﴿6﴾',
      'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ ﴿7﴾',
    ],
  ),

  // مثال لسورة قصيرة (الإخلاص)
  SurahData(
    number: 112,
    name: 'Al-Ikhlas',
    nameAr: 'الإخلاص',
    ayahCount: 4,
    type: 'مكية',
    juzNumber: 30,
    verses: [
      'قُلْ هُوَ اللَّهُ أَحَدٌ ﴿1﴾',
      'اللَّهُ الصَّمَدُ ﴿2﴾',
      'لَمْ يَلِدْ وَلَمْ يُولَدْ ﴿3﴾',
      'وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ ﴿4﴾',
    ],
  ),

  // يمكنك متابعة الإضافة: بقية السور 2..111 ثم 113، 114 وهكذا.
];
