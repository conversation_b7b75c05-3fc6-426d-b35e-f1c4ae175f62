import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:provider/provider.dart';
import '../providers/app_settings.dart';

class QuranAudioPlayer extends StatefulWidget {
  final String surahName;
  final int ayahNumber;
  final String readerCode;
  final String readerName;
  final int surahNumber;
  final int ayahCount;

  const QuranAudioPlayer({
    Key? key,
    required this.surahName,
    required this.ayahNumber,
    required this.readerCode,
    required this.readerName,
    required this.surahNumber,
    required this.ayahCount,
  }) : super(key: key);

  @override
  State<QuranAudioPlayer> createState() => _QuranAudioPlayerState();
}

class _QuranAudioPlayerState extends State<QuranAudioPlayer> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initAudioPlayer();
  }

  Future<void> _initAudioPlayer() async {
    // Set player mode and volume for optimal playback
    await _audioPlayer.setPlayerMode(PlayerMode.mediaPlayer);
    await _audioPlayer.setVolume(1.0); // Maximum volume
    await _audioPlayer.setReleaseMode(ReleaseMode.stop);

    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
        });
      }
    });

    _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() => _duration = duration);
      }
    });

    _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() => _position = position);
      }
    });

    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _isPlaying = false;
          _position = Duration.zero;
        });
      }
    });
  }

  Future<void> _playAyah() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
      return;
    }

    if (_position.inSeconds > 0) {
      await _audioPlayer.resume();
      return;
    }

    setState(() => _isLoading = true);
    debugPrint(
        'بدء تشغيل الآية ${widget.ayahNumber} من السورة ${widget.surahNumber} بقراءة ${widget.readerName}');

    try {
      // Ensure maximum volume before playing
      await _audioPlayer.setVolume(1.0);
      debugPrint('تم تعيين مستوى الصوت إلى الحد الأقصى');

      final surahStr = widget.surahNumber.toString().padLeft(3, '0');
      final ayahStr = widget.ayahNumber.toString().padLeft(3, '0');

      // Try multiple audio sources for better reliability
      final audioUrls = [
        'https://everyayah.com/data/${widget.readerCode}/$surahStr$ayahStr.mp3',
        'https://server.mp3quran.net/${widget.readerCode}/$surahStr$ayahStr.mp3',
        'https://cdn.islamic.network/quran/audio/128/${widget.readerCode}/${widget.surahNumber}/${widget.ayahNumber}.mp3',
      ];

      bool playbackSuccessful = false;

      for (String audioUrl in audioUrls) {
        try {
          debugPrint('محاولة تشغيل من الرابط: $audioUrl');
          await _audioPlayer.play(UrlSource(audioUrl));

          // Wait to verify playback started
          await Future.delayed(const Duration(milliseconds: 500));

          if (_audioPlayer.state == PlayerState.playing) {
            debugPrint('نجح التشغيل من الرابط: $audioUrl');
            playbackSuccessful = true;
            break;
          } else {
            debugPrint('فشل التشغيل - حالة المشغل: ${_audioPlayer.state}');
          }
        } catch (e) {
          debugPrint('فشل تشغيل الرابط: $audioUrl - $e');
          continue;
        }
      }

      if (!playbackSuccessful) {
        throw Exception('فشل في تشغيل الآية من جميع المصادر');
      }
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تشغيل التلاوة. تفاصيل الخطأ: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress bar
          Slider(
            value: _position.inSeconds.toDouble(),
            min: 0,
            max: _duration.inSeconds.toDouble(),
            onChanged: (value) {
              _audioPlayer.seek(Duration(seconds: value.toInt()));
            },
            activeColor: settings.appColor,
            inactiveColor: settings.appColor.withOpacity(0.3),
          ),

          // Time and controls
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_position.inMinutes}:${(_position.inSeconds % 60).toString().padLeft(2, '0')}',
                  style: const TextStyle(fontSize: 12),
                ),
                Text(
                  '${_duration.inMinutes}:${(_duration.inSeconds % 60).toString().padLeft(2, '0')}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Player controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Reader and surah info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${widget.readerName} - ${widget.surahName}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textDirection: TextDirection.rtl,
                    ),
                    Text(
                      'آية ${widget.ayahNumber} من ${widget.ayahCount}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).hintColor,
                      ),
                    ),
                  ],
                ),
              ),

              // Play/Pause button
              IconButton(
                icon: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            settings.appColor,
                          ),
                        ),
                      )
                    : Icon(
                        _isPlaying
                            ? Icons.pause_circle_filled
                            : Icons.play_circle_filled,
                        size: 36,
                        color: settings.appColor,
                      ),
                onPressed: _isLoading ? null : _playAyah,
              ),

              // Stop button
              IconButton(
                icon: const Icon(Icons.stop, size: 30),
                onPressed: () {
                  _audioPlayer.stop();
                  setState(() {
                    _isPlaying = false;
                    _position = Duration.zero;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
