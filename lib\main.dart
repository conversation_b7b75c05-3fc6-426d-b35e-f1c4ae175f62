import 'dart:async';
import 'package:azkar_moslim/screens/tasbih_screen.dart';
import 'package:azkar_moslim/services/audio_handler.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_material_color_picker/flutter_material_color_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'data/azkar_data.dart' as azkar;
import 'providers/app_settings.dart';
import 'providers/favorites_provider.dart';
import 'utils/arabic_date_utils.dart';
import 'package:azkar_moslim/screens/quran_screen.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:azkar_moslim/utils/date_initializer.dart';
import 'utils/app_initializer.dart';
import 'package:azkar_moslim/utils/initializer.dart';
import 'data/extended_azkar.dart';
import 'data/prophets_stories.dart';
import 'data/quran_tafseer.dart';
import 'data/prayers_data.dart';
import 'services/quran_service.dart';
import 'package:audio_service/audio_service.dart';

Future<void> main() async {
  try {
    debugPrint('Starting app initialization...');
    WidgetsFlutterBinding.ensureInitialized();
    debugPrint('WidgetsFlutterBinding.ensureInitialized() completed');

    debugPrint('Initializing app settings...');
    final settings = AppSettings();
    await settings.init();
    debugPrint('App settings initialized successfully');

    // Initialize QuranService
    debugPrint('Initializing Quran service...');
    final quranService = QuranService();
    await quranService.initialize();
    debugPrint('Quran service initialized successfully');

    // Initialize AudioHandler
    debugPrint('Initializing Audio service...');
    final audioHandler = await initAudioService();
    debugPrint('Audio service initialized successfully');

    debugPrint('Starting app...');
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider<AppSettings>.value(value: settings),
          ChangeNotifierProvider(create: (_) => FavoritesProvider()),
          Provider<QuranService>.value(value: quranService),
          Provider<AudioHandler>.value(value: audioHandler),
        ],
        child: Phoenix(child: const MyApp()),
      ),
    );
  } catch (e, stackTrace) {
    debugPrint('Error in main: $e');
    debugPrint('Stack trace: $stackTrace');
    // Even if there\u0027s an error, we still want to run the app
    runApp(const MyApp());
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppSettings>(
      builder: (context, settings, _) {
        return MaterialApp(
          title: 'أذكار المسلم',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: settings.primaryColor,
            colorScheme: ColorScheme.fromSeed(
              seedColor: settings.primaryColor,
              primary: settings.primaryColor,
              secondary: settings.secondaryColor,
            ),
            scaffoldBackgroundColor:
                Colors.grey[50],
            cardColor: Colors.white,
            fontFamily: 'Cairo',
          ),
          darkTheme: ThemeData.dark().copyWith(
            primaryColor: settings.primaryColor,
            colorScheme: ColorScheme.fromSeed(
              seedColor: settings.primaryColor,
              primary: settings.primaryColor,
              secondary: settings.secondaryColor,
              brightness: Brightness.dark,
            ),
            scaffoldBackgroundColor: const Color(0xFF000000),
            cardColor: const Color(0xFF161616),
            canvasColor: const Color(0xFF000000),
            dialogBackgroundColor: const Color(0xFF161616),
            bottomSheetTheme: const BottomSheetThemeData(
              backgroundColor: Color(0xFF161616),
            ),
            dividerColor: Colors.white12,
            textTheme: const TextTheme(
              bodyLarge: TextStyle(color: Colors.white),
              bodyMedium: TextStyle(color: Colors.white70),
              titleLarge: TextStyle(color: Colors.white, fontSize: 20),
            ).apply(bodyColor: Colors.white, displayColor: Colors.white),
          ),
          themeMode: settings.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar', '')],
          locale: const Locale('ar', ''),
          home: const SplashScreen(),
        );
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
      ),
    );

    _controller.forward();
    _navigateToHome();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _navigateToHome() async {
    await Future.delayed(
      const Duration(seconds: 2),
    );
    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const MyHomePage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppSettings>(
      builder: (context, settings, _) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/Background.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: Center(
              child: AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/icon.png',
                            width: 100,
                            height: 100,
                          ),
                          const SizedBox(height: 20),
                          Text(
                            'أذكار المسلم',
                            style: GoogleFonts.cairo(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 30),
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.grey[800]!,
                              ),
                              strokeWidth: 3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int _selectedIndex = -1;
  Timer? _timer;
  DateTime? _lastBackPressed;
  String _currentTime = '';
  String _hijriDate = '';
  String _gregorianDate = '';

  final Map<int, ScrollController> _scrollControllers = {};
  final Map<int, double> _savedScrollPositions = {};
  final Map<int, String> _savedSearchQueries = {};
  final Map<int, int> _savedSelectedItems = {};
  ScrollController? _currentScrollController;

  int _lastSelectedCategory = -1;
  final ScrollController _mainGridController = ScrollController();
  bool _shouldHighlightLastCategory = false;

  @override
  void initState() {
    super.initState();
    _updateDateTime();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _searchController.dispose();
    for (var controller in _scrollControllers.values) {
      controller.dispose();
    }
    _currentScrollController?.dispose();
    _mainGridController.dispose();
    super.dispose();
  }

  ScrollController _getScrollController(int categoryIndex) {
    if (!_scrollControllers.containsKey(categoryIndex)) {
      _scrollControllers[categoryIndex] = ScrollController();
    }
    return _scrollControllers[categoryIndex]!;
  }

  void _saveCurrentPageState() {
    if (_selectedIndex != -1 && _currentScrollController != null) {
      _savedScrollPositions[_selectedIndex] =
          _currentScrollController!.hasClients
              ? _currentScrollController!.offset
              : 0.0;

      _savedSearchQueries[_selectedIndex] = _searchQuery;

      _lastSelectedCategory = _selectedIndex;

      print(
        'Saved state for category $_selectedIndex: scroll=${_savedScrollPositions[_selectedIndex]}, search="$_searchQuery"',
      );
    }
  }

  void _restorePageState(int categoryIndex) {
    final savedQuery = _savedSearchQueries[categoryIndex] ?? '';
    _searchQuery = savedQuery;
    _searchController.text = savedQuery;

    _currentScrollController = _getScrollController(categoryIndex);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_currentScrollController != null &&
          _currentScrollController!.hasClients &&
          _savedScrollPositions.containsKey(categoryIndex)) {
        final savedPosition = _savedScrollPositions[categoryIndex]!;
        final maxScroll = _currentScrollController!.position.maxScrollExtent;
        final targetPosition = savedPosition.clamp(0.0, maxScroll);

        _currentScrollController!.animateTo(
          targetPosition,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

        print(
          'Restored state for category $categoryIndex: scroll=$targetPosition, search="$savedQuery"',
        );
      }
    });
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });

    if (_selectedIndex != -1) {
      _savedSearchQueries[_selectedIndex] = value;
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateDateTime();
    });
  }

  Future<void> _updateDateTime() async {
    try {
      final time = await ArabicDateUtils.formatArabicTime(DateTime.now());
      final hijri = await ArabicDateUtils.getHijriDate();
      final gregorian = await ArabicDateUtils.getGregorianDate();

      if (mounted) {
        setState(() {
          _currentTime = time;
          _hijriDate = hijri;
          _gregorianDate = gregorian;
        });
      }
    } catch (e) {
      debugPrint('Error updating date/time: $e');
    }
  }

  List<Map<String, dynamic>> _getAzkarList() {
    if (_selectedIndex == -1) return [];

    switch (_selectedIndex) {
      case 0:
        final favorites = Provider.of<FavoritesProvider>(
          context,
          listen: false,
        );
        return _filterAzkar(_getFavoriteAzkar(favorites.favorites));
      case 1:
        return _filterAzkar(azkar.morningAzkar);
      case 2:
        return _filterAzkar(azkar.eveningAzkar);
      case 3:
        return _filterAzkar(azkar.sleepAzkar);
      case 4:
        return _filterAzkar(azkar.prayerAzkar);
      case 5:
        return _filterAzkar(azkar.wudhuAzkar);
      case 6:
        return _filterAzkar(azkar.hajjAzkar);
      case 7:
        return _filterAzkar(azkar.foodAzkar);
      case 8:
        return _filterAzkar(azkar.travelAzkar);
      case 9:
        return _filterAzkar(azkar.homeAzkar);
      case 10:
        return _filterAzkar(azkar.marketAzkar);
      case 11:
        return _filterAzkar(azkar.studyAzkar);
      case 12:
        return _filterAzkar(azkar.weatherAzkar);
      case 13:
        return _filterAzkar(azkar.tasbeehAzkar);
      case 15:
        return _filterAzkar(quranAzkar);
      case 16:
        return _filterAzkar(wisdomAzkar);
      case 17:
        return _filterAzkar(workAzkar);
      case 18:
        return _filterAzkar(healthAzkar);
      case 19:
        return _filterAzkar(familyAzkar);
      case 20:
        return _filterAzkar(forgivenessAzkar);
      case 21:
        return _filterAzkar(blessingsAzkar);
      case 22:
        return _filterAzkar(duhaAzkar);
      case 23:
        return _filterAzkar(nightAzkar);
      case 24:
        return _filterAzkar(stressAzkar);
      case 25:
        return _filterAzkar(joyAzkar);
      case 26:
        return _filterAzkar(protectionAzkar);
      case 27:
        return morningEveningAzkar;
      case 28:
        return prophetsStories;
      case 29:
        return quranTafseer;
      case 30:
        return shortTafseer;
      case 31:
        return _filterAzkar(prayers);
      default:
        return [];
    }
  }

  void _showSettingsDialog() {
    showDialog(context: context, builder: (context) => const SettingsDialog());
  }

  List<Map<String, dynamic>> _filterAzkar(
    List<Map<String, dynamic>> azkarList,
  ) {
    if (_searchQuery.isEmpty) return azkarList;
    return azkarList.where((zekr) {
      final zekrText = zekr['zekr'] as String;
      final zekrDesc = zekr['description'] as String;
      return zekrText.contains(_searchQuery) || zekrDesc.contains(_searchQuery);
    }).toList();
  }

  List<Map<String, dynamic>> _getFavoriteAzkar(Set<String> favorites) {
    List<Map<String, dynamic>> allAzkar = [];

    void addAzkarFromCategory(List<Map<String, dynamic>> category) {
      for (var zekr in category) {
        if (favorites.contains(zekr['zekr'])) {
          allAzkar.add({...zekr});
        }
      }
    }

    addAzkarFromCategory(azkar.morningAzkar);
    addAzkarFromCategory(azkar.eveningAzkar);
    addAzkarFromCategory(azkar.sleepAzkar);
    addAzkarFromCategory(azkar.prayerAzkar);
    addAzkarFromCategory(azkar.wudhuAzkar);
    addAzkarFromCategory(azkar.hajjAzkar);
    addAzkarFromCategory(azkar.foodAzkar);
    addAzkarFromCategory(azkar.travelAzkar);
    addAzkarFromCategory(azkar.homeAzkar);
    addAzkarFromCategory(azkar.marketAzkar);
    addAzkarFromCategory(azkar.studyAzkar);
    addAzkarFromCategory(azkar.weatherAzkar);
    addAzkarFromCategory(azkar.tasbeehAzkar);
    addAzkarFromCategory(quranAzkar);
    addAzkarFromCategory(wisdomAzkar);
    addAzkarFromCategory(workAzkar);
    addAzkarFromCategory(healthAzkar);
    addAzkarFromCategory(familyAzkar);
    addAzkarFromCategory(forgivenessAzkar);
    addAzkarFromCategory(blessingsAzkar);
    addAzkarFromCategory(blessingsAzkar);
    addAzkarFromCategory(prayers);

    return allAzkar;
  }

  Future<bool> _onWillPop() async {
    if (_selectedIndex != -1) {
      _saveCurrentPageState();

      setState(() {
        _selectedIndex = -1;
        _searchQuery = '';
        _searchController.clear();
        _currentScrollController = null;
        _shouldHighlightLastCategory = true;
      });

      if (_lastSelectedCategory != -1) {
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollToCategory(_lastSelectedCategory);
        });
      }

      return false;
    }

    if (_lastBackPressed == null ||
        DateTime.now().difference(_lastBackPressed!) >
            const Duration(seconds: 2)) {
      _lastBackPressed = DateTime.now();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اضغط مرة أخرى للخروج'),
          duration: Duration(seconds: 2),
        ),
      );
      return false;
    }
    return true;
  }

  void _scrollToCategory(int categoryIndex) {
    if (!_mainGridController.hasClients) return;

    final rowIndex = categoryIndex ~/ 2;
    final itemHeight =
        160.0;
    final headerHeight = 120.0;

    final targetPosition = headerHeight + (rowIndex * itemHeight);
    final maxScroll = _mainGridController.position.maxScrollExtent;
    final clampedPosition = targetPosition.clamp(0.0, maxScroll);

    _mainGridController.animateTo(
      clampedPosition,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );

    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        setState(() {
          _shouldHighlightLastCategory = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final azkarList = _getAzkarList();
    final settings = Provider.of<AppSettings>(context);

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: settings.backgroundColor,
        appBar: AppBar(
          backgroundColor: settings.primaryColor,
          elevation: 0,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
          ),
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  settings.primaryColor,
                  settings.primaryColor.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          title: Text(
            'أذكار المسلم',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: IconButton(icon: const Icon(Icons.settings, color: Colors.white), onPressed: _showSettingsDialog),
            ),
          ],
        ),
        body: Column(
          children: [
            if (_selectedIndex == -1)
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.symmetric(
                  vertical: 16,
                  horizontal: 24,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    colors: [settings.accentColor, settings.secondaryColor],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: settings.accentColor.withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.access_time_rounded,
                          color: Colors.white,
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          _currentTime,
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _hijriDate,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _gregorianDate,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            Expanded(
              child: _selectedIndex == -1
                  ? GridView.count(
                      controller: _mainGridController,
                      crossAxisCount: 5,
                      padding: const EdgeInsets.all(16),
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      children: [
                        _buildCategoryButton('المفضلة', Icons.favorite, 0),
                        _buildCategoryButton('أذكار الصباح', Icons.wb_sunny, 1),
                        _buildCategoryButton(
                          'أذكار المساء',
                          Icons.nights_stay,
                          2,
                        ),
                        _buildCategoryButton('أذكار النوم', Icons.bedtime, 3),
                        _buildCategoryButton('أذكار الصلاة', Icons.mosque, 4),
                        _buildCategoryButton(
                          'أذكار الوضوء',
                          Icons.water_drop,
                          5,
                        ),
                        _buildCategoryButton(
                          'أذكار الحج',
                          Icons.account_balance,
                          6,
                        ),
                        _buildCategoryButton(
                          'أذكار الطعام',
                          Icons.restaurant,
                          7,
                        ),
                        _buildCategoryButton('أذكار السفر', Icons.flight, 8),
                        _buildCategoryButton('أذكار المنزل', Icons.home, 9),
                        _buildCategoryButton(
                          'أذكار السوق',
                          Icons.shopping_cart,
                          10,
                        ),
                        _buildCategoryButton('أذكار العلم', Icons.school, 11),
                        _buildCategoryButton('أذكار الطقس', Icons.cloud, 12),
                        _buildCategoryButton(
                          'السبحة الإلكترونية',
                          Icons.touch_app,
                          14,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TasbihScreen(),
                                fullscreenDialog: true,
                              ),
                            );
                          },
                        ),
                        _buildCategoryButton('أذكار القرآن', Icons.book, 15),
                        _buildCategoryButton(
                          'أذكار الحكمة',
                          Icons.lightbulb,
                          16,
                        ),
                        _buildCategoryButton('أذكار العمل', Icons.work, 17),
                        _buildCategoryButton('أذكار الصحة', Icons.healing, 18),
                        _buildCategoryButton(
                          'أذكار الأسرة',
                          Icons.family_restroom,
                          19,
                        ),
                        _buildCategoryButton(
                          'أذكار الاستغفار',
                          Icons.favorite,
                          20,
                        ),
                        _buildCategoryButton('أذكار الشكر', Icons.stars, 21),
                        _buildCategoryButton(
                          'أذكار الضحى',
                          Icons.wb_sunny_outlined,
                          22,
                        ),
                        _buildCategoryButton(
                          'أذكار الليل',
                          Icons.nightlight_round,
                          23,
                        ),
                        _buildCategoryButton(
                          'أذكار الكرب',
                          Icons.healing_outlined,
                          24,
                        ),
                        _buildCategoryButton(
                          'أذكار الفرح',
                          Icons.celebration,
                          25,
                        ),
                        _buildCategoryButton(
                          'أذكار الحماية',
                          Icons.security,
                          26,
                        ),
                        _buildCategoryButton(
                          'صباح ومساء',
                          Icons.brightness_4,
                          27,
                        ),
                        _buildCategoryButton(
                          'قصص الأنبياء',
                          Icons.auto_stories,
                          28,
                        ),
                        _buildCategoryButton(
                          'تفسير القرآن',
                          Icons.book_outlined,
                          29,
                        ),
                        _buildCategoryButton(
                          'تفسير مختصر',
                          Icons.menu_book_outlined,
                          30,
                        ),
                        _buildCategoryButton(
                          'الأدعية المأثورة',
                          Icons.volunteer_activism,
                          31,
                        ),
                        _buildCategoryButton(
                          'القرآن الكريم',
                          Icons.menu_book,
                          32,
                          onTap: () {
                            if (_selectedIndex != -1 && _selectedIndex != 32) {
                              _saveCurrentPageState();
                            }

                            _lastSelectedCategory = 32;

                            setState(() {
                              _shouldHighlightLastCategory = false;
                            });

                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const QuranScreen(),
                              ),
                            ).then((_) {
                              if (mounted) {
                                setState(() {
                                  _shouldHighlightLastCategory = true;
                                });

                                Future.delayed(
                                  const Duration(milliseconds: 300),
                                  () {
                                    _scrollToCategory(32);
                                  },
                                );
                              }
                            });
                          },
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.arrow_back),
                                onPressed: () {
                                  _saveCurrentPageState();

                                  setState(() {
                                    _selectedIndex = -1;
                                    _searchQuery = '';
                                    _searchController.clear();
                                    _currentScrollController = null;
                                    _shouldHighlightLastCategory = true;
                                  });

                                  if (_lastSelectedCategory != -1) {
                                    Future.delayed(
                                      const Duration(milliseconds: 300),
                                      () {
                                        _scrollToCategory(
                                          _lastSelectedCategory,
                                        );
                                      },
                                    );
                                  }
                                },
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _getCategoryTitle(_selectedIndex),
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: AzkarList(
                            azkarList: azkarList,
                            scrollController: _currentScrollController,
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryTitle(int index) {
    switch (index) {
      case 0:
        return 'المفضلة';
      case 1:
        return 'أذكار الصباح';
      case 2:
        return 'أذكار المساء';
      case 3:
        return 'أذكار النوم';
      case 4:
        return 'أذكار الصلاة';
      case 5:
        return 'أذكار الوضوء';
      case 6:
        return 'أذكار الحج';
      case 7:
        return 'أذكار الطعام';
      case 8:
        return 'أذكار السفر';
      case 9:
        return 'أذكار المنزل';
      case 10:
        return 'أذكار السوق';
      case 11:
        return 'أذكار العلم';
      case 12:
        return 'أذكار الطقس';
      case 13:
        return 'التسبيح';
      case 15:
        return 'أذكار القرآن';
      case 16:
        return 'أذكار الحكمة';
      case 17:
        return 'أذكار العمل';
      case 18:
        return 'أذكار الصحة';
      case 19:
        return 'أذكار الأسرة';
      case 20:
        return 'أذكار الاستغفار';
      case 21:
        return 'أذكار الشكر';
      case 22:
        return 'أذكار الضحى';
      case 23:
        return 'أذكار الليل';
      case 24:
        return 'أذكار الكرب';
      case 25:
        return 'أذكار الفرح';
      case 26:
        return 'أذكار الحماية';
      case 27:
        return 'أذكار الصباح والمساء';
      case 28:
        return 'قصص الأنبياء';
      case 29:
        return 'تفسير القرآن';
      case 30:
        return 'تفسير مختصر';
      case 31:
        return 'الأدعية المأثورة';
      case 32:
        return 'القرآن الكريم';

      default:
        return '';
    }
  }

  Widget _buildCategoryButton(
    String title,
    IconData icon,
    int index,
    {
      VoidCallback? onTap,
    }
  ) {
    final settings = Provider.of<AppSettings>(context);
    final favorites = Provider.of<FavoritesProvider>(context);
    final favoriteCount = index == 0 ? favorites.favorites.length : null;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final isLastSelected =
        _shouldHighlightLastCategory && _lastSelectedCategory == index;

    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF161616)
            : Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: isLastSelected
            ? Border.all(color: settings.primaryColor, width: 3)
            : null,
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.5)
                : isLastSelected
                    ? settings.primaryColor.withOpacity(
                        0.4,
                      )
                    : settings.primaryColor.withOpacity(0.1),
            blurRadius: isLastSelected ? 16 : 12,
            spreadRadius: isDark ? 1 : (isLastSelected ? 3 : 2),
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap ??
              () {
                if (_selectedIndex != -1 && _selectedIndex != index) {
                  _saveCurrentPageState();
                }

                setState(() {
                  _selectedIndex = index;
                  _shouldHighlightLastCategory = false;
                });

                _restorePageState(index);
              },
          borderRadius: BorderRadius.circular(24),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            settings.primaryColor,
                            settings.primaryColor.withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: settings.primaryColor.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(icon, size: 32, color: Colors.white),
                    ),
                    if (favoriteCount != null && favoriteCount > 0)
                      Positioned(
                        right: -8,
                        top: -8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                settings.accentColor,
                                settings.accentColor.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: settings.accentColor.withOpacity(0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            '$favoriteCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : settings.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class AzkarList extends StatelessWidget {
  final List<Map<String, dynamic>> azkarList;
  final ScrollController? scrollController;

  const AzkarList({super.key, required this.azkarList, this.scrollController});

  @override
  Widget build(BuildContext context) {
    if (azkarList.isEmpty) {
      return const Center(
        child: Text('لا توجد أذكار', style: TextStyle(fontSize: 18)),
      );
    }

    if (azkarList.first.containsKey('name')) {
      return ListView.builder(
        controller: scrollController,
        itemCount: azkarList.length,
        itemBuilder: (context, index) {
          return ProphetStoryCard(story: azkarList[index]);
        },
      );
    } else if (azkarList.first.containsKey('surah') ||
        azkarList.first.containsKey('title')) {
      return ListView.builder(
        controller: scrollController,
        itemCount: azkarList.length,
        itemBuilder: (context, index) {
          if (azkarList[index].containsKey('title')) {
            return ShortTafseerCard(tafseer: azkarList[index]);
          }
          return TafseerCard(tafseer: azkarList[index]);
        },
      );
    }

    return ListView.builder(
      controller: scrollController,
      itemCount: azkarList.length,
      itemBuilder: (context, index) {
        return ZekrCard(zekr: azkarList[index]);
      },
    );
  }
}

class ShortTafseerCard extends StatelessWidget {
  final Map<String, dynamic> tafseer;

  const ShortTafseerCard({super.key, required this.tafseer});

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);
    final theme = Theme.of(context);
    final baseFontSize = settings.fontSize.clamp(14.0, 26.0);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          tafseer['title'] ?? '',
          style: TextStyle(
            fontSize: baseFontSize,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        subtitle: Text(
          (tafseer['ayat'] as List?)?.firstOrNull?['text'] ?? '',
          style: TextStyle(
            fontSize: baseFontSize - 2,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...(tafseer['ayat'] as List? ?? []).map(
                  (ayah) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ayah['surah'] ?? '',
                          style: TextStyle(
                            fontSize: baseFontSize - 2,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          ayah['text'] ?? '',
                          style: TextStyle(
                            fontSize: baseFontSize - 2,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          ayah['explanation'] ?? '',
                          style: TextStyle(
                            fontSize: baseFontSize - 4,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProphetStoryCard extends StatelessWidget {
  final Map<String, dynamic> story;

  const ProphetStoryCard({super.key, required this.story});

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);
    final theme = Theme.of(context);
    final baseFontSize = settings.fontSize.clamp(14.0, 26.0);
    final titleFontSize = baseFontSize;
    final subtitleFontSize = baseFontSize - 4;
    final textFontSize = baseFontSize - 2;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          story['name'],
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        subtitle: Text(
          story['title'],
          style: TextStyle(
            fontSize: subtitleFontSize,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  story['summary'],
                  style: TextStyle(
                    fontSize: textFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  story['story'],
                  style: TextStyle(fontSize: textFontSize, height: 1.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'الدروس المستفادة:',
                  style: TextStyle(
                    fontSize: textFontSize,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                ...List.generate(
                  (story['lessons'] as List).length,
                  (index) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Icon(
                          Icons.circle,
                          size: 8,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            story['lessons'][index],
                            style: TextStyle(fontSize: textFontSize),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                if (story['dua'] != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'الدعاء:',
                    style: TextStyle(
                      fontSize: textFontSize,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      story['dua'],
                      style: TextStyle(
                        fontSize: textFontSize,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TafseerCard extends StatelessWidget {
  final Map<String, dynamic> tafseer;

  const TafseerCard({super.key, required this.tafseer});

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);
    final theme = Theme.of(context);
    final baseFontSize = settings.fontSize.clamp(14.0, 26.0);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          tafseer['surah'] ?? '',
          style: TextStyle(
            fontSize: baseFontSize,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        subtitle: Text(
          tafseer['ayat'] ?? '',
          style: TextStyle(
            fontSize: baseFontSize - 2,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tafseer['tafseer'] ?? '',
                  style: TextStyle(fontSize: baseFontSize - 2, height: 1.5),
                ),
                if (tafseer['benefits'] != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'الفوائد:',
                    style: TextStyle(
                      fontSize: baseFontSize - 2,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...(tafseer['benefits'] as List).map(
                    (benefit) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.circle,
                            size: 8,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              benefit,
                              style: TextStyle(fontSize: baseFontSize - 4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ZekrCard extends StatefulWidget {
  final Map<String, dynamic> zekr;

  const ZekrCard({super.key, required this.zekr});

  @override
  State<ZekrCard> createState() => _ZekrCardState();
}

class _ZekrCardState extends State<ZekrCard> {
  int _counter = 0;

  void _incrementCounter() {
    if (_counter < (widget.zekr['count'] as int)) {
      setState(() {
        _counter++;
      });
    }
  }

  void _resetCounter() {
    setState(() {
      _counter = 0;
    });
  }

  void _shareZekr() {
    Share.share(
      '${widget.zekr['zekr']}\n\n${widget.zekr['description']}\n\nعدد المرات: ${widget.zekr['count']}',
    );
  }

  void _copyZekr() {
    FlutterClipboard.copy(widget.zekr['zekr']).then((_) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم نسخ الذكر')));
    });
  }

  void _toggleFavorite() {
    final favoritesProvider = Provider.of<FavoritesProvider>(
      context,
      listen: false,
    );
    final zekrText = widget.zekr['zekr'] as String;

    setState(() {
      if (favoritesProvider.isFavorite(zekrText)) {
        favoritesProvider.removeFavorite(zekrText);
      } else {
        favoritesProvider.addFavorite(zekrText);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);
    final isFavorite = favoritesProvider.isFavorite(widget.zekr['zekr']);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final double zekrFontSize = settings.fontSize.clamp(14, 26);
    final double descriptionFontSize = (zekrFontSize - 2).clamp(12, 22);
    final double buttonFontSize = (zekrFontSize - 4).clamp(12, 18);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(zekrFontSize * 0.8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              widget.zekr['zekr'],
              style: TextStyle(
                fontSize: zekrFontSize,
                height: 1.5,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : theme.textTheme.bodyLarge?.color,
              ),
              textAlign: TextAlign.right,
            ),
            SizedBox(height: zekrFontSize * 0.5),
            Text(
              widget.zekr['description'],
              style: TextStyle(
                fontSize: descriptionFontSize,
                height: 1.4,
                color: theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
              ),
              textAlign: TextAlign.right,
            ),
            Divider(height: zekrFontSize * 1.2),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: _shareZekr,
                      tooltip: 'مشاركة',
                      iconSize: buttonFontSize * 1.5,
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: _copyZekr,
                      tooltip: 'نسخ',
                      iconSize: buttonFontSize * 1.5,
                    ),
                    IconButton(
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey,
                      ),
                      onPressed: _toggleFavorite,
                      tooltip:
                          isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
                      iconSize: buttonFontSize * 1.5,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: buttonFontSize * 0.8,
                        vertical: buttonFontSize * 0.4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(buttonFontSize),
                      ),
                      child: Text(
                        '$_counter/${widget.zekr['count']}',
                        style: TextStyle(
                          fontSize: buttonFontSize,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _counter < (widget.zekr['count'] as int)
                          ? _incrementCounter
                          : null,
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(buttonFontSize),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: buttonFontSize,
                          vertical: buttonFontSize * 0.5,
                        ),
                      ),
                      child: Text(
                        _counter < (widget.zekr['count'] as int)
                            ? 'تسبيح'
                            : 'تم',
                        style: TextStyle(fontSize: buttonFontSize),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _resetCounter,
                      tooltip: 'إعادة تعيين',
                      iconSize: buttonFontSize * 1.5,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsDialog extends StatelessWidget {
  const SettingsDialog({super.key});

  void _showColorPicker(
    BuildContext context,
    Color currentColor,
    Future<void> Function(Color) onColorChanged,
  ) {
    Color tempColor = currentColor;

    showDialog(
      context: context,
      builder: (context) => Consumer<AppSettings>(
        builder: (context, settings, _) => AlertDialog(
          title: const Text('اختر اللون'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'درجات اللون الأسود',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildCustomColorButton(
                      const Color(0xFF000000),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF161616),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF1E1E1E),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF2D2D2D),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'الألوان الرئيسية',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildCustomColorButton(
                      const Color(0xFF1F4690),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF066163),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF2E7D32),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                    _buildCustomColorButton(
                      const Color(0xFF7B2869),
                      tempColor,
                      (color) => tempColor = color,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'جميع الألوان',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                MaterialColorPicker(
                  selectedColor: tempColor,
                  onColorChange: (color) => tempColor = color,
                  colors: Colors.primaries,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  await onColorChanged(tempColor);
                  if (context.mounted) {
                    await settings.setPrimaryColor(tempColor);
                    await settings.setSecondaryColor(
                      tempColor.withOpacity(0.8),
                    );
                    await settings.setAccentColor(tempColor.withOpacity(0.6));
                    Phoenix.rebirth(context);
                  }
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('حدث خطأ أثناء حفظ اللون')),
                    );
                  }
                }
              },
              child: const Text('تم'),
            ),
          ],
        ),
      ),
    );
  }

  void _showCustomColorPicker(
    BuildContext context,
    Color currentColor,
    Future<void> Function(Color) onColorChanged,
  ) {
    Color tempColor = currentColor;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'درجات اللون الأسود',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildCustomColorButton(
                    const Color(0xFF000000),
                    tempColor,
                    (color) => tempColor = color,
                  ),
                  _buildCustomColorButton(
                    const Color(0xFF161616),
                    tempColor,
                    (color) => tempColor = color,
                  ),
                  _buildCustomColorButton(
                    const Color(0xFF1E1E1E),
                    tempColor,
                    (color) => tempColor = color,
                  ),
                  _buildCustomColorButton(
                    const Color(0xFF2D2D2D),
                    tempColor,
                    (color) => tempColor = color,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              MaterialColorPicker(
                selectedColor: tempColor,
                onColorChange: (color) => tempColor = color,
                colors: Colors.primaries,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await onColorChanged(tempColor);
                if (context.mounted) {
                  Navigator.pop(context);
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('حدث خطأ أثناء حفظ اللون')),
                  );
                }
              }
            },
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomColorButton(
    Color color,
    Color selectedColor,
    void Function(Color) onSelect,
  ) {
    return InkWell(
      onTap: () => onSelect(color),
      child: Container(
        width: 45,
        height: 45,
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: selectedColor == color ? Colors.white : Colors.transparent,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppSettings>(
      builder: (context, settings, _) {
        return AlertDialog(
          title: const Text('الإعدادات'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SwitchListTile(
                  title: const Text('الوضع الليلي'),
                  value: settings.isDarkMode,
                  onChanged: (value) async {
                    try {
                      await settings.setDarkMode(value);
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('حدث خطأ أثناء حفظ الإعداد'),
                          ),
                        );
                      }
                    }
                  },
                ),
                ListTile(
                  title: const Text('لون التطبيق الرئيسي'),
                  trailing: CircleAvatar(
                    backgroundColor: settings.primaryColor,
                    radius: 15,
                  ),
                  onTap: () {
                    _showColorPicker(
                      context,
                      settings.primaryColor,
                      (color) => settings.setPrimaryColor(color),
                    );
                  },
                ),
                ListTile(
                  title: const Text('حجم الخط'),
                  subtitle: Slider(
                    value: settings.fontSize,
                    min: 12,
                    max: 28,
                    divisions: 8,
                    label: settings.fontSize.round().toString(),
                    onChanged: (value) async {
                      try {
                        await settings.setFontSize(value);
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('حدث خطأ أثناء حفظ حجم الخط'),
                            ),
                          );
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }
}