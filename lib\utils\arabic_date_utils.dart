import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:hijri/hijri_calendar.dart';
import 'date_initializer.dart';

class ArabicDateUtils {
  static final _numberFormat = NumberFormat('00', 'ar');

  static Future<String> formatArabicTime(DateTime time) async {
    if (!DateInitializer.isInitialized) {
      await DateInitializer.initialize();
    }

    try {
      final hour12 =
          time.hour > 12 ? time.hour - 12 : (time.hour == 0 ? 12 : time.hour);
      final period = time.hour < 12 ? 'صباحاً' : 'مساءً';
      return '${_numberFormat.format(hour12)}:${_numberFormat.format(time.minute)} $period';
    } catch (e) {
      debugPrint('Error formatting time: $e');
      return '--:--';
    }
  }

  static final _arabicNumbers = [
    '٠',
    '١',
    '٢',
    '٣',
    '٤',
    '٥',
    '٦',
    '٧',
    '٨',
    '٩'
  ];

  static final _arabicMonths = [
    'محرم',
    'صفر',
    'ربيع الأول',
    'ربيع الثاني',
    'جمادى الأولى',
    'جمادى الآخرة',
    'رجب',
    'شعبان',
    'رمضان',
    'شوال',
    'ذو القعدة',
    'ذو الحجة'
  ];

  static final _arabicWeekDays = [
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد'
  ];

  static String _convertToArabicNumber(String number) {
    String result = '';
    for (int i = 0; i < number.length; i++) {
      if (number[i].contains(RegExp(r'[0-9]'))) {
        result += _arabicNumbers[int.parse(number[i])];
      } else {
        result += number[i];
      }
    }
    return result;
  }

  static Future<void> _ensureInitialized() async {
    await DateInitializer.initialize();
  }

  static Future<String> formatArabicDate(DateTime date) async {
    await _ensureInitialized();
    final hijri = HijriCalendar.fromDate(date);
    final weekDay = _arabicWeekDays[date.weekday - 1];
    final hijriDay = _convertToArabicNumber(hijri.hDay.toString());
    final hijriMonth = _arabicMonths[hijri.hMonth - 1];
    final hijriYear = _convertToArabicNumber(hijri.hYear.toString());

    final gregorianDay = _convertToArabicNumber(date.day.toString());
    final gregorianMonth = DateFormat.MMMM('ar').format(date);
    final gregorianYear = _convertToArabicNumber(date.year.toString());

    return '$weekDay $hijriDay من $hijriMonth $hijriYear هـ\n'
        'الموافق $gregorianDay من $gregorianMonth $gregorianYear م';
  }

  static Future<String> getHijriDate() async {
    await DateInitializer.initialize();
    try {
      final hijri = HijriCalendar.now();
      final day = _convertToArabicNumber(hijri.hDay.toString());
      final month = _arabicMonths[hijri.hMonth - 1];
      final year = _convertToArabicNumber(hijri.hYear.toString());
      return '$day من $month $year هـ';
    } catch (e) {
      debugPrint('Error getting Hijri date: $e');
      return '';
    }
  }

  static Future<String> getGregorianDate() async {
    await DateInitializer.initialize();
    try {
      final now = DateTime.now();
      final formatter = DateFormat('d MMMM yyyy', 'ar');
      final dateStr = formatter.format(now);
      return '${_convertToArabicNumber(dateStr)} م';
    } catch (e) {
      debugPrint('Error getting Gregorian date: $e');
      return '';
    }
  }

  static String _getHijriMonthName(int month) {
    const List<String> months = [
      'محرم',
      'صفر',
      'ربيع الأول',
      'ربيع الثاني',
      'جمادى الأولى',
      'جمادى الآخرة',
      'رجب',
      'شعبان',
      'رمضان',
      'شوال',
      'ذو القعدة',
      'ذو الحجة'
    ];
    return months[month - 1];
  }
}
