import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:azkar_moslim/models/quran_model.dart';

class QuranService {
  static final QuranService _instance = QuranService._internal();
  factory QuranService() => _instance;
  QuranService._internal();

  Map<String, List<Map<String, dynamic>>>? _quranData;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final String jsonString = await rootBundle.loadString('assets/quran.json');
      final Map<String, dynamic> decoded = json.decode(jsonString);
      _quranData = decoded.map((key, value) => MapEntry(
        key,
        (value as List).cast<Map<String, dynamic>>(),
      ));
      _isInitialized = true;
    } catch (e) {
      print('Error loading Quran data: $e');
      rethrow;
    }
  }

  List<QuranAyah> getSurahAyahs(int surahNumber) {
    if (!_isInitialized) {
      throw Exception('QuranService not initialized. Call initialize() first.');
    }

    final surahKey = surahNumber.toString();
    if (_quranData!.containsKey(surahKey)) {
      return _quranData![surahKey]!
          .map((ayah) => QuranAyah.fromJson(ayah))
          .toList();
    }
    return [];
  }

  List<int> getSurahNumbers() {
    if (!_isInitialized) {
      throw Exception('QuranService not initialized. Call initialize() first.');
    }
    
    return _quranData!.keys.map(int.parse).toList()..sort();
  }

  String getAyahText(int surahNumber, int ayahNumber) {
    final ayahs = getSurahAyahs(surahNumber);
    try {
      return ayahs.firstWhere((ayah) => ayah.verse == ayahNumber).text;
    } catch (e) {
      return '';
    }
  }

  String getSurahText(int surahNumber) {
    final ayahs = getSurahAyahs(surahNumber);
    return ayahs.map((ayah) => ayah.text).join(' ');
  }
}
