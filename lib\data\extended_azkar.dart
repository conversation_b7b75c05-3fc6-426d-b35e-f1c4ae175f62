final List<Map<String, dynamic>> quranAzkar = [
  {
    'zekr': 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
    'description': 'تقال قبل قراءة القرآن',
    'count': 1
  },
  {
    'zekr': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
    'description': 'الاستعاذة قبل قراءة القرآن',
    'count': 1
  },
];

final List<Map<String, dynamic>> wisdomAzkar = [
  {
    'zekr':
        'اللَّهُمَّ عَلِّمْنِي مَا يَنْفَعُنِي، وَانْفَعْنِي بِمَا عَلَّمْتَنِي، وَزِدْنِي عِلْمًا',
    'description': 'دعاء طلب العلم',
    'count': 3
  },
  {
    'zekr': 'رَبِّ زِدْنِي عِلْماً',
    'description': 'دعاء طلب زيادة العلم',
    'count': 3
  },
];

final List<Map<String, dynamic>> workAzkar = [
  {
    'zekr':
        'بِسْمِ اللَّهِ تَوَكَّلْتُ عَلَى اللَّهِ، وَلا حَوْلَ وَلا قُوَّةَ إِلا بِاللَّهِ',
    'description': 'يقال عند بداية العمل',
    'count': 1
  },
  {
    'zekr': 'اللَّهُمَّ بَارِكْ لِي فِي رِزْقِي',
    'description': 'دعاء البركة في العمل',
    'count': 3
  },
];

final List<Map<String, dynamic>> healthAzkar = [
  {
    'zekr': 'اللَّهُمَّ اشْفِ مَرْضَانَا، وَارْحَمْ مَوْتَانَا',
    'description': 'دعاء للمريض',
    'count': 3
  },
  {
    'zekr': 'أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ، اشْفِ وَأَنْتَ الشَّافِي',
    'description': 'دعاء الشفاء',
    'count': 3
  },
];

final List<Map<String, dynamic>> familyAzkar = [
  {
    'zekr':
        'رَبِّ اجْعَلْنِي مُقِيمَ الصَّلَاةِ وَمِن ذُرِّيَّتِي ۚ رَبَّنَا وَتَقَبَّلْ دُعَاءِ',
    'description': 'دعاء للذرية',
    'count': 3
  },
  {
    'zekr':
        'رَبَّنَا هَبْ لَنَا مِنْ أَزْوَاجِنَا وَذُرِّيَّاتِنَا قُرَّةَ أَعْيُنٍ وَاجْعَلْنَا لِلْمُتَّقِينَ إِمَامًا',
    'description': 'دعاء للأهل والذرية',
    'count': 3
  },
];

final List<Map<String, dynamic>> forgivenessAzkar = [
  {
    'zekr': 'أَسْتَغْفِرُ اللَّهَ وَأَتُوبُ إِلَيْهِ',
    'description': 'الاستغفار',
    'count': 100
  },
  {
    'zekr': 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ سُبْحَانَ اللَّهِ الْعَظِيمِ',
    'description': 'التسبيح',
    'count': 100
  },
];

final List<Map<String, dynamic>> blessingsAzkar = [
  {
    'zekr': 'الْحَمْدُ لِلَّهِ الَّذِي بِنِعْمَتِهِ تَتِمُّ الصَّالِحَاتُ',
    'description': 'الحمد على النعم',
    'count': 3
  },
  {
    'zekr':
        'اللَّهُمَّ لَكَ الْحَمْدُ كَمَا يَنْبَغِي لِجَلَالِ وَجْهِكَ وَعَظِيمِ سُلْطَانِكَ',
    'description': 'الحمد والثناء',
    'count': 3
  },
];

final List<Map<String, dynamic>> duhaAzkar = [
  {
    'zekr':
        'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ عَدَدَ خَلْقِهِ وَرِضَا نَفْسِهِ وَزِنَةَ عَرْشِهِ وَمِدَادَ كَلِمَاتِهِ',
    'description': 'تسبيحات الضحى',
    'count': 3
  },
  {
    'zekr':
        'اللَّهُمَّ إِنِّي أَسْأَلُكَ رِزْقًا طَيِّبًا، وَعِلْمًا نَافِعًا، وَعَمَلًا مُتَقَبَّلًا',
    'description': 'دعاء الضحى',
    'count': 3
  },
];

final List<Map<String, dynamic>> nightAzkar = [
  {
    'zekr': 'اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ',
    'description': 'دعاء قيام الليل',
    'count': 3
  },
  {
    'zekr': 'رَبَّنَا لَا تُؤَاخِذْنَا إِنْ نَسِينَا أَوْ أَخْطَأْنَا',
    'description': 'دعاء آخر الليل',
    'count': 3
  },
];

final List<Map<String, dynamic>> stressAzkar = [
  {
    'zekr':
        'لَا إِلَهَ إِلَّا أَنْتَ سُبْحَانَكَ إِنِّي كُنْتُ مِنَ الظَّالِمِينَ',
    'description': 'دعاء الكرب',
    'count': 7
  },
  {
    'zekr':
        'حَسْبِيَ اللَّهُ لَا إِلَهَ إِلَّا هُوَ عَلَيْهِ تَوَكَّلْتُ وَهُوَ رَبُّ الْعَرْشِ الْعَظِيمِ',
    'description': 'دعاء الهم والحزن',
    'count': 7
  },
];

final List<Map<String, dynamic>> joyAzkar = [
  {
    'zekr': 'الحَمْدُ لِلَّهِ الَّذِي بِنِعْمَتِهِ تَتِمُّ الصَّالِحَاتُ',
    'description': 'دعاء الفرح',
    'count': 3
  },
  {
    'zekr':
        'اللَّهُمَّ لَكَ الْحَمْدُ حَتَّى تَرْضَى، وَلَكَ الْحَمْدُ إِذَا رَضِيتَ، وَلَكَ الْحَمْدُ بَعْدَ الرِّضَا',
    'description': 'دعاء السرور والنعمة',
    'count': 3
  },
];

final List<Map<String, dynamic>> protectionAzkar = [
  {
    'zekr':
        'بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ',
    'description': 'دعاء الحفظ',
    'count': 3
  },
  {
    'zekr': 'أَعُوذُ بِكَلِمَاتِ اللَّهِ التَّامَّاتِ مِنْ شَرِّ مَا خَلَقَ',
    'description': 'دعاء الحماية',
    'count': 3
  },
];

final List<Map<String, dynamic>> morningEveningAzkar = [
  {
    'zekr':
        'اللَّهُمَّ بِكَ أَصْبَحْنَا، وَبِكَ أَمْسَيْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ',
    'description': 'ذكر الصباح والمساء',
    'count': 1
  },
  {
    'zekr':
        'اللَّهُمَّ إِنِّي أَسْأَلُكَ خَيْرَ هَذَا الْيَوْمِ: فَتْحَهُ، وَنَصْرَهُ، وَنُورَهُ، وَبَرَكَتَهُ، وَهُدَاهُ',
    'description': 'دعاء بداية اليوم',
    'count': 1
  },
];
