import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class SurahReaderScreen extends StatefulWidget {
  final int surahNumber;
  final String surahName;
  final List<Map<String, dynamic>> verses;
  final Function(int, int) onPlayVerse;
  final int? currentPlayingVerse;
  final bool isPlaying;
  final int? bookmarkedVerse;
  final Function(int)? onBookmarkVerse;

  const SurahReaderScreen({
    Key? key,
    required this.surahNumber,
    required this.surahName,
    required this.verses,
    required this.onPlayVerse,
    this.currentPlayingVerse,
    this.isPlaying = false,
    this.bookmarkedVerse,
    this.onBookmarkVerse,
  }) : super(key: key);

  @override
  State<SurahReaderScreen> createState() => _SurahReaderScreenState();
}

class _SurahReaderScreenState extends State<SurahReaderScreen> {
  late ScrollController _scrollController;
  double _fontSize = 20.0;
  bool _showTashkeel = true;
  bool _useTraditionalVerseEnding =
      true; // Toggle between circle and traditional ending
  int? _localBookmarkedVerse; // Local state for immediate UI updates

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _localBookmarkedVerse =
        widget.bookmarkedVerse; // Initialize local bookmark state
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToVerse(int verseNumber) {
    try {
      if (verseNumber <= widget.verses.length && verseNumber > 0) {
        if (_scrollController.hasClients && mounted) {
          double position =
              (verseNumber - 1) * 120.0; // Approximate verse height
          // Ensure position doesn't exceed max scroll extent
          final maxScrollExtent = _scrollController.position.maxScrollExtent;
          if (position > maxScrollExtent) {
            position = maxScrollExtent;
          }

          _scrollController.animateTo(
            position,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      }
    } catch (e) {
      debugPrint('Error scrolling to verse $verseNumber: $e');
    }
  }

  // Build traditional Quran-style verse ending symbol
  Widget _buildTraditionalVerseEnding(int verseNumber, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      margin: const EdgeInsets.symmetric(horizontal: 3),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            theme.primaryColor.withOpacity(0.1),
            theme.primaryColor.withOpacity(0.05),
          ],
        ),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.4),
          width: 1.2,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.primaryColor.withOpacity(0.2),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // رمز بداية الآية
          Text(
            '۝', // ۝ - Traditional Quran verse ending symbol
            style: TextStyle(
              fontSize: _fontSize - 1,
              color: theme.primaryColor,
              fontWeight: FontWeight.w600,
              height: 1.0,
            ),
          ),
          const SizedBox(width: 2),
          // رقم الآية بالأرقام العربية
          Text(
            _convertToArabicNumerals(verseNumber),
            style: TextStyle(
              fontSize: _fontSize - 3,
              fontWeight: FontWeight.bold,
              color: theme.primaryColor,
              fontFamily: 'Traditional Arabic', // خط عربي تقليدي
              height: 1.0,
            ),
          ),
          const SizedBox(width: 2),
          // رمز نهاية الآية
          Text(
            '۝', // ۝ - Traditional Quran verse ending symbol
            style: TextStyle(
              fontSize: _fontSize - 1,
              color: theme.primaryColor,
              fontWeight: FontWeight.w600,
              height: 1.0,
            ),
          ),
        ],
      ),
    );
  }

  // Build traditional Quran-style verse number
  Widget _buildTraditionalVerseNumber(int verseNumber, ThemeData theme) {
    return Container(
      width: 36,
      height: 36,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: Alignment.center,
          colors: [
            Colors.white,
            theme.primaryColor.withOpacity(0.03),
            theme.primaryColor.withOpacity(0.08),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
        border: Border.all(
          color: theme.primaryColor,
          width: 2.8,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.primaryColor.withOpacity(0.35),
            blurRadius: 8,
            offset: const Offset(0, 3),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.8),
            blurRadius: 2,
            offset: const Offset(0, -1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Stack(
        children: [
          // حلقة داخلية مزخرفة
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.all(7),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: theme.primaryColor.withOpacity(0.5),
                  width: 1.8,
                ),
              ),
            ),
          ),
          // حلقة داخلية أصغر
          Positioned(
            top: 5,
            left: 5,
            right: 5,
            bottom: 5,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: theme.primaryColor.withOpacity(0.25),
                  width: 0.8,
                ),
              ),
            ),
          ),
          // نقاط زخرفية صغيرة
          Positioned(
            top: 3,
            right: 3,
            child: Container(
              width: 3,
              height: 3,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.primaryColor.withOpacity(0.4),
              ),
            ),
          ),
          Positioned(
            bottom: 3,
            left: 3,
            child: Container(
              width: 3,
              height: 3,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.primaryColor.withOpacity(0.4),
              ),
            ),
          ),
          // رقم الآية
          Center(
            child: Text(
              _convertToArabicNumerals(verseNumber),
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
                height: 1.0,
                fontFamily: 'Traditional Arabic',
                shadows: [
                  Shadow(
                    color: Colors.white.withOpacity(0.8),
                    offset: const Offset(0.5, 0.5),
                    blurRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Convert numbers to Arabic numerals (improved)
  String _convertToArabicNumerals(int number) {
    const arabicNumerals = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩'
    };

    return number.toString().split('').map((digit) {
      return arabicNumerals[digit] ?? digit;
    }).join();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.surahName,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          backgroundColor: theme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            // Audio Controls Section
            if (widget.isPlaying || widget.currentPlayingVerse != null) ...[
              // Previous verse button
              IconButton(
                icon: const Icon(Icons.skip_previous, size: 24),
                onPressed: widget.currentPlayingVerse != null &&
                        widget.currentPlayingVerse! > 1
                    ? () => widget.onPlayVerse(
                        widget.surahNumber, widget.currentPlayingVerse! - 1)
                    : null,
                tooltip: 'الآية السابقة',
              ),
              // Play/Pause button
              IconButton(
                icon: Icon(
                  widget.isPlaying ? Icons.pause : Icons.play_arrow,
                  size: 28,
                ),
                onPressed: () {
                  if (widget.currentPlayingVerse != null) {
                    widget.onPlayVerse(
                        widget.surahNumber, widget.currentPlayingVerse!);
                  }
                },
                tooltip: widget.isPlaying ? 'إيقاف مؤقت' : 'تشغيل',
              ),
              // Next verse button
              IconButton(
                icon: const Icon(Icons.skip_next, size: 24),
                onPressed: widget.currentPlayingVerse != null &&
                        widget.currentPlayingVerse! < widget.verses.length
                    ? () => widget.onPlayVerse(
                        widget.surahNumber, widget.currentPlayingVerse! + 1)
                    : null,
                tooltip: 'الآية التالية',
              ),
              // Divider
              Container(
                width: 1,
                height: 30,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                color: Colors.white.withOpacity(0.3),
              ),
            ],
            // Settings menu
            PopupMenuButton(
              icon: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.more_vert, size: 20),
              ),
              tooltip: 'خيارات العرض',
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'numbering',
                  child: Row(
                    children: [
                      Icon(_useTraditionalVerseEnding
                          ? Icons.circle_outlined
                          : Icons.more_horiz),
                      const SizedBox(width: 8),
                      Text('نمط ترقيم الآيات'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'font_size',
                  child: Row(
                    children: [
                      const Icon(Icons.format_size),
                      const SizedBox(width: 8),
                      const Text('حجم الخط'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'tashkeel',
                  child: Row(
                    children: [
                      Icon(_showTashkeel
                          ? Icons.visibility
                          : Icons.visibility_off),
                      const SizedBox(width: 8),
                      Text(_showTashkeel ? 'إخفاء التشكيل' : 'إظهار التشكيل'),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'font_size') {
                  _showFontSizeDialog();
                } else if (value == 'tashkeel') {
                  setState(() {
                    _showTashkeel = !_showTashkeel;
                  });
                } else if (value == 'numbering') {
                  setState(() {
                    _useTraditionalVerseEnding = !_useTraditionalVerseEnding;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          Icon(
                            _useTraditionalVerseEnding
                                ? Icons.check_circle
                                : Icons.circle,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _useTraditionalVerseEnding
                                ? 'تم التبديل إلى الترقيم التقليدي'
                                : 'تم التبديل إلى الترقيم الدائري',
                          ),
                        ],
                      ),
                      duration: const Duration(seconds: 2),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: theme.primaryColor,
                    ),
                  );
                }
              },
            ),
          ],
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                theme.primaryColor.withOpacity(0.05),
                Colors.white,
              ],
            ),
          ),
          child: Column(
            children: [
              // Surah header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.primaryColor.withOpacity(0.1),
                      theme.primaryColor.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.primaryColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'سورة ${widget.surahName}',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: theme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bookmark,
                          size: 16,
                          color: theme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${widget.verses.length} آية',
                          style: TextStyle(
                            color: theme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.numbers,
                          size: 16,
                          color: theme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'رقم ${widget.surahNumber}',
                          style: TextStyle(
                            color: theme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Bismillah (except for Surah 9)
              if (widget.surahNumber != 9 && widget.surahNumber != 1)
                Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: _fontSize + 4,
                      fontWeight: FontWeight.bold,
                      color: theme.primaryColor,
                      height: 1.8,
                    ),
                  ),
                ),

              // Verses list
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: widget.verses.length,
                  itemBuilder: (context, index) {
                    final verse = widget.verses[index];
                    final verseNumber = verse['verse'] ?? (index + 1);
                    final verseText = verse['text'] ?? '';
                    final isCurrentVerse =
                        widget.currentPlayingVerse == verseNumber;
                    final isBookmarkedVerse =
                        (_localBookmarkedVerse ?? widget.bookmarkedVerse) ==
                            verseNumber;

                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: isCurrentVerse
                            ? LinearGradient(
                                colors: [
                                  theme.primaryColor.withOpacity(0.15),
                                  theme.primaryColor.withOpacity(0.05),
                                ],
                                begin: Alignment.topRight,
                                end: Alignment.bottomLeft,
                              )
                            : isBookmarkedVerse
                                ? LinearGradient(
                                    colors: [
                                      Colors.amber.withOpacity(0.15),
                                      Colors.amber.withOpacity(0.05),
                                    ],
                                    begin: Alignment.topRight,
                                    end: Alignment.bottomLeft,
                                  )
                                : LinearGradient(
                                    colors: [
                                      Colors.white,
                                      Colors.grey.withOpacity(0.02),
                                    ],
                                  ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isCurrentVerse
                              ? theme.primaryColor.withOpacity(0.6)
                              : isBookmarkedVerse
                                  ? Colors.amber.withOpacity(0.8)
                                  : Colors.grey.withOpacity(0.15),
                          width: isCurrentVerse || isBookmarkedVerse ? 2.5 : 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: isCurrentVerse
                                ? theme.primaryColor.withOpacity(0.2)
                                : isBookmarkedVerse
                                    ? Colors.amber.withOpacity(0.2)
                                    : Colors.black.withOpacity(0.08),
                            blurRadius:
                                isCurrentVerse || isBookmarkedVerse ? 12 : 8,
                            offset: const Offset(0, 4),
                            spreadRadius:
                                isCurrentVerse || isBookmarkedVerse ? 2 : 0,
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Verse text with integrated verse number
                          RichText(
                            textAlign: TextAlign.justify,
                            textDirection: TextDirection.rtl,
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: _showTashkeel
                                      ? verseText
                                      : _removeTashkeel(verseText),
                                  style: TextStyle(
                                    fontSize: _fontSize,
                                    height: 1.8,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                    fontFamily:
                                        'Arial', // You can use a custom Arabic font
                                  ),
                                ),
                                const TextSpan(
                                    text: ' '), // Space before verse number
                                WidgetSpan(
                                  alignment: PlaceholderAlignment.middle,
                                  child: _useTraditionalVerseEnding
                                      ? _buildTraditionalVerseEnding(
                                          verseNumber, theme)
                                      : _buildTraditionalVerseNumber(
                                          verseNumber, theme),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Verse controls
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Left side - Status indicators
                              Row(
                                children: [
                                  // Bookmark indicator
                                  if (isBookmarkedVerse)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.amber.withOpacity(0.3),
                                            Colors.amber.withOpacity(0.1),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Colors.amber,
                                          width: 1.5,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: const [
                                          Icon(
                                            Icons.bookmark,
                                            color: Colors.amber,
                                            size: 14,
                                          ),
                                          SizedBox(width: 4),
                                          Text(
                                            'مؤشر',
                                            style: TextStyle(
                                              color: Colors.amber,
                                              fontSize: 11,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                  // Playing indicator
                                  if (isCurrentVerse && widget.isPlaying)
                                    Container(
                                      margin: EdgeInsets.only(
                                        left: isBookmarkedVerse ? 8 : 0,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            theme.primaryColor.withOpacity(0.3),
                                            theme.primaryColor.withOpacity(0.1),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: theme.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.volume_up,
                                            color: theme.primaryColor,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            'يتم التشغيل',
                                            style: TextStyle(
                                              color: theme.primaryColor,
                                              fontSize: 11,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),

                              // Right side - Action buttons
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Bookmark button
                                  if (widget.onBookmarkVerse != null)
                                    Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: isBookmarkedVerse
                                            ? Colors.amber.withOpacity(0.1)
                                            : Colors.grey.withOpacity(0.05),
                                        border: Border.all(
                                          color: isBookmarkedVerse
                                              ? Colors.amber.withOpacity(0.3)
                                              : Colors.grey.withOpacity(0.2),
                                        ),
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          isBookmarkedVerse
                                              ? Icons.bookmark
                                              : Icons.bookmark_border,
                                          color: isBookmarkedVerse
                                              ? Colors.amber
                                              : Colors.grey[600],
                                          size: 22,
                                        ),
                                        onPressed: () {
                                          try {
                                            if (widget.onBookmarkVerse !=
                                                    null &&
                                                verseNumber >= 1 &&
                                                verseNumber <=
                                                    widget.verses.length) {
                                              // Update local state immediately for instant UI feedback
                                              setState(() {
                                                _localBookmarkedVerse =
                                                    verseNumber;
                                              });

                                              // Call the parent callback
                                              widget.onBookmarkVerse!(
                                                  verseNumber);

                                              // Show success feedback
                                              if (mounted &&
                                                  context.mounted &&
                                                  Scaffold.maybeOf(context) !=
                                                      null) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Row(
                                                      children: [
                                                        const Icon(
                                                            Icons.bookmark,
                                                            color:
                                                                Colors.white),
                                                        const SizedBox(
                                                            width: 8),
                                                        Expanded(
                                                          child: Text(
                                                              'تم حفظ الإشارة المرجعية عند الآية $verseNumber'),
                                                        ),
                                                      ],
                                                    ),
                                                    backgroundColor:
                                                        Colors.amber,
                                                    behavior: SnackBarBehavior
                                                        .floating,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                  ),
                                                );
                                              }
                                            }
                                          } catch (e) {
                                            debugPrint(
                                                'Error saving bookmark: $e');
                                            // Show error message to user
                                            if (mounted &&
                                                context.mounted &&
                                                Scaffold.maybeOf(context) !=
                                                    null) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                  content: Row(
                                                    children: [
                                                      const Icon(
                                                          Icons.error_outline,
                                                          color: Colors.white),
                                                      const SizedBox(width: 8),
                                                      const Expanded(
                                                        child: Text(
                                                            'حدث خطأ في حفظ الإشارة المرجعية'),
                                                      ),
                                                    ],
                                                  ),
                                                  backgroundColor: Colors.red,
                                                  behavior:
                                                      SnackBarBehavior.floating,
                                                ),
                                              );
                                            }
                                          }
                                        },
                                        tooltip: 'إضافة إشارة مرجعية',
                                      ),
                                    ),

                                  const SizedBox(width: 8),

                                  // Play button
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          theme.primaryColor.withOpacity(0.1),
                                          theme.primaryColor.withOpacity(0.05),
                                        ],
                                      ),
                                      border: Border.all(
                                        color:
                                            theme.primaryColor.withOpacity(0.3),
                                      ),
                                    ),
                                    child: IconButton(
                                      icon: Icon(
                                        isCurrentVerse && widget.isPlaying
                                            ? Icons.pause_circle_filled
                                            : Icons.play_circle_filled,
                                        color: theme.primaryColor,
                                        size: 32,
                                      ),
                                      onPressed: () {
                                        widget.onPlayVerse(
                                            widget.surahNumber, verseNumber);
                                        _scrollToVerse(verseNumber);
                                      },
                                      tooltip: 'تشغيل الآية',
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // Floating action buttons
        floatingActionButton: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            FloatingActionButton.small(
              heroTag: "play_surah",
              onPressed: () => widget.onPlayVerse(widget.surahNumber, 1),
              backgroundColor: theme.primaryColor,
              child: const Icon(Icons.play_arrow, color: Colors.white),
              tooltip: 'تشغيل السورة كاملة',
            ),
            const SizedBox(height: 8),
            FloatingActionButton.small(
              heroTag: "scroll_to_top",
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              backgroundColor: Colors.grey[600],
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
              tooltip: 'العودة للأعلى',
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('حجم الخط'),
          content: StatefulBuilder(
            builder: (context, setStateDialog) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('الحجم الحالي: ${_fontSize.toInt()}'),
                  Slider(
                    value: _fontSize,
                    min: 14.0,
                    max: 32.0,
                    divisions: 18,
                    onChanged: (value) {
                      setStateDialog(() {
                        _fontSize = value;
                      });
                      setState(() {
                        _fontSize = value;
                      });
                    },
                  ),
                  Text(
                    'مثال على النص بالحجم المحدد',
                    style: TextStyle(fontSize: _fontSize),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('تم'),
            ),
          ],
        ),
      ),
    );
  }

  String _removeTashkeel(String text) {
    // Remove Arabic diacritical marks (tashkeel)
    return text.replaceAll(RegExp(r'[\u064B-\u0652\u0670\u0640]'), '');
  }
}
