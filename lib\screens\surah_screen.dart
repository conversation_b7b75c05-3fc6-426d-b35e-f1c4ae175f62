import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_settings.dart';
import '../models/surah_model.dart';

class SurahScreen extends StatefulWidget {
  final Surah surah;
  final Function(int)? onVerseTap;
  final int? currentPlayingVerse;
  final bool isPlaying;

  const SurahScreen({
    Key? key,
    required this.surah,
    this.onVerseTap,
    this.currentPlayingVerse,
    this.isPlaying = false,
  }) : super(key: key);

  @override
  _SurahScreenState createState() => _SurahScreenState();
}

class _SurahScreenState extends State<SurahScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Helper method to convert numbers to Arabic numerals
  String _toArabicNumber(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) => arabicNumbers[int.parse(digit)]).join('');
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<AppSettings>(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.surah.name,
            style: const TextStyle(fontFamily: 'UthmanicHafs', fontSize: 24),
          ),
          centerTitle: true,
        ),
        body: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          itemCount: widget.surah.numberOfAyahs,
          itemBuilder: (context, index) {
            final verseNumber = index + 1;
            final isPlaying = widget.currentPlayingVerse == verseNumber && widget.isPlaying;
            
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              elevation: 2,
              child: ListTile(
                title: Text(
                  '(${_toArabicNumber(verseNumber)})',
                  style: TextStyle(
                    color: isPlaying 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey[700],
                    fontWeight: isPlaying ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
                subtitle: Text(
                  '﷽', // Placeholder for actual verse text
                  style: TextStyle(
                    fontFamily: 'UthmanicHafs',
                    fontSize: settings.fontSize,
                    height: 1.8,
                    color: isPlaying ? Theme.of(context).primaryColor : null,
                  ),
                  textAlign: TextAlign.right,
                ),
                trailing: isPlaying
                    ? const Icon(Icons.volume_up, color: Colors.blue)
                    : null,
                onTap: widget.onVerseTap != null 
                    ? () => widget.onVerseTap!(verseNumber)
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }
}
