import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:hijri/hijri_calendar.dart';

class AppInitializer {
  static bool _initialized = false;

  static Future<void> initializeApp() async {
    if (!_initialized) {
      try {
        // تهيئة Flutter
        WidgetsFlutterBinding.ensureInitialized();

        // تهيئة التواريخ
        await Future.wait([
          initializeDateFormatting('ar', null),
          initializeDateFormatting('ar_SA', null),
        ]);

        Intl.defaultLocale = 'ar';
        HijriCalendar.setLocal('ar');

        _initialized = true;
        debugPrint('App initialized successfully');
      } catch (e) {
        debugPrint('Error initializing app: $e');
        rethrow;
      }
    }
  }
}
