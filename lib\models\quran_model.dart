class QuranAyah {
  final int chapter;
  final int verse;
  final String text;

  QuranAyah({
    required this.chapter,
    required this.verse,
    required this.text,
  });

  factory QuranAyah.fromJson(Map<String, dynamic> json) {
    return QuranAyah(
      chapter: json['chapter'],
      verse: json['verse'],
      text: json['text'],
    );
  }
}

class QuranSurah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final String revelationType;
  final List<QuranAyah> ayahs;

  QuranSurah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.revelationType,
    required this.ayahs,
  });

  factory QuranSurah.fromJson(Map<String, dynamic> json) {
    return QuranSurah(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      englishNameTranslation: json['englishNameTranslation'],
      revelationType: json['revelationType'],
      ayahs: (json['ayahs'] as List)
          .map((ayah) => QuranAyah.fromJson(ayah))
          .toList(),
    );
  }
}

class QuranData {
  final List<QuranSurah> surahs;

  QuranData({required this.surahs});

  factory QuranData.fromJson(Map<String, dynamic> json) {
    return QuranData(
      surahs: (json['data'] as List)
          .map((surah) => QuranSurah.fromJson(surah))
          .toList(),
    );
  }
}
