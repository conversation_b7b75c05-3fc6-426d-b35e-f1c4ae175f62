import 'dart:async';
import 'dart:io' show TimeoutException;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:azkar_moslim/data/quran_data.dart';
import 'package:azkar_moslim/models/quran_model.dart';
import 'package:azkar_moslim/services/quran_service.dart';
import 'package:azkar_moslim/data/surah_names.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';

class JuzScreen extends StatefulWidget {
  final JuzData juz;

  const JuzScreen({Key? key, required this.juz}) : super(key: key);

  @override
  _JuzScreenState createState() => _JuzScreenState();
}

class _JuzScreenState extends State<JuzScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  late PageController _pageController;
  bool _isLoading = true;
  List<List<QuranAyah>> _quranPages = [];
  int _currentPage = 0;
  bool _isPlaying = false;
  bool _isPaused = false;
  int _currentSurah = 1;
  int _currentVerse = 1;
  String _selectedReader = 'ماهر المعيقلي';
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<void>? _playerCompleteSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadJuzPages();
    _initializeAudioPlayer();
    _loadPreferences();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _playerStateSubscription?.cancel();
    _playerCompleteSubscription?.cancel();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _loadJuzPages() async {
    final quranService = Provider.of<QuranService>(context, listen: false);
    final List<QuranAyah> ayahs = [];
    final startParts = widget.juz.start.split(':');
    final startVerse = int.parse(startParts[1]);
    final startSurahParts = startParts[0].split(' ');
    final startSurah = int.parse(startSurahParts.last);
    final endParts = widget.juz.end.split(':');
    final endVerse = int.parse(endParts[1]);
    final endSurahParts = endParts[0].split(' ');
    final endSurah = int.parse(endSurahParts.last);

    for (int surahNumber in widget.juz.surahs) {
      final surahAyahs = quranService.getSurahAyahs(surahNumber);
      if (surahNumber == startSurah && surahNumber == endSurah) {
        ayahs.addAll(surahAyahs.where(
            (ayah) => ayah.verse >= startVerse && ayah.verse <= endVerse));
      } else if (surahNumber == startSurah) {
        ayahs.addAll(surahAyahs.where((ayah) => ayah.verse >= startVerse));
      } else if (surahNumber == endSurah) {
        ayahs.addAll(surahAyahs.where((ayah) => ayah.verse <= endVerse));
      } else {
        ayahs.addAll(surahAyahs);
      }
    }

    const versesPerPage = 10;
    List<List<QuranAyah>> pages = [];
    for (int i = 0; i < ayahs.length; i += versesPerPage) {
      final end =
          (i + versesPerPage < ayahs.length) ? i + versesPerPage : ayahs.length;
      pages.add(ayahs.sublist(i, end));
    }

    setState(() {
      _quranPages = pages;
      _isLoading = false;
    });
  }

  void _initializeAudioPlayer() async {
    await _audioPlayer.setAudioContext(
      AudioContext(
        iOS: AudioContextIOS(
            category: AVAudioSessionCategory.playback,
            options: {AVAudioSessionOptions.mixWithOthers}),
        android: AudioContextAndroid(
            isSpeakerphoneOn: false,
            stayAwake: true,
            contentType: AndroidContentType.music,
            usageType: AndroidUsageType.media,
            audioFocus: AndroidAudioFocus.gain),
      ),
    );
    _audioPlayer.setReleaseMode(ReleaseMode.stop);
    await _audioPlayer.setVolume(1.0);
    _playerStateSubscription =
        _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
          _isPaused = state == PlayerState.paused;
        });
      }
    });
    _playerCompleteSubscription = _audioPlayer.onPlayerComplete.listen((event) {
      if (mounted) {
        _playNextVerse();
      }
    });
    _durationSubscription = _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() => _duration = duration);
      }
    });
    _positionSubscription = _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() => _position = position);
      }
    });
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedReader = prefs.getString('selected_reader') ?? 'ماهر المعيقلي';
    });
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_reader', _selectedReader);
  }

  Future<void> _playVerse(QuranAyah ayah) async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _currentSurah = ayah.chapter;
      _currentVerse = ayah.verse;
    });
    try {
      await _playVerseLocally(ayah.chapter, ayah.verse);
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _playVerseLocally(int surahNumber, int verseNumber) async {
    try {
      await _audioPlayer.stop();
      final s3 = surahNumber.toString().padLeft(3, '0');
      final v3 = verseNumber.toString().padLeft(3, '0');
      final readerCode =
          _quranReaders[_selectedReader]?['code'] ?? 'MaherAlMuaiqly128kbps';
      final url = 'https://everyayah.com/data/$readerCode/${s3}${v3}.mp3';

      await _audioPlayer.play(UrlSource(url)).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException(
              'Audio playback timed out', const Duration(seconds: 10));
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  void _playNextVerse() {
    int currentVerseIndex = -1;
    final allAyahs = _quranPages.expand((page) => page).toList();
    for (int i = 0; i < allAyahs.length; i++) {
      if (allAyahs[i].chapter == _currentSurah &&
          allAyahs[i].verse == _currentVerse) {
        currentVerseIndex = i;
        break;
      }
    }

    if (currentVerseIndex != -1) {
      if (currentVerseIndex + 1 < allAyahs.length) {
        final nextAyah = allAyahs[currentVerseIndex + 1];
        _playVerse(nextAyah);
      }
    }
  }

  void _playPreviousVerse() {
    int currentVerseIndex = -1;
    final allAyahs = _quranPages.expand((page) => page).toList();
    for (int i = 0; i < allAyahs.length; i++) {
      if (allAyahs[i].chapter == _currentSurah &&
          allAyahs[i].verse == _currentVerse) {
        currentVerseIndex = i;
        break;
      }
    }

    if (currentVerseIndex > 0) {
      final prevAyah = allAyahs[currentVerseIndex - 1];
      _playVerse(prevAyah);
    }
  }

  Future<void> _togglePlayPause() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
    } else {
      if (_isPaused) {
        await _audioPlayer.resume();
      } else {
        final currentPageVerses = _quranPages[_currentPage];
        if (currentPageVerses.isNotEmpty) {
          _playVerse(currentPageVerses.first);
        }
      }
    }
  }

  Future<void> _stopPlayback() async {
    await _audioPlayer.stop();
  }

  String _getArabicNumber(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number
        .toString()
        .split('')
        .map((digit) => arabicNumbers[int.parse(digit)])
        .join();
  }

  String _getSurahName(int surahNumber) {
    if (surahNumber >= 1 && surahNumber <= 114) {
      return surahNames[surahNumber - 1];
    }
    return '';
  }

  Widget _buildQuranPage(int pageIndex) {
    if (pageIndex < 0 || pageIndex >= _quranPages.length) {
      return const Center(child: Text('Page not found'));
    }
    final pageVerses = _quranPages[pageIndex];
    if (pageVerses.isEmpty) {
      return const Center(child: Text('No verses on this page'));
    }
    final List<InlineSpan> textSpans = [];
    int lastSurahNumber = -1;
    for (int i = 0; i < pageVerses.length; i++) {
      final verse = pageVerses[i];
      if (verse.chapter != lastSurahNumber) {
        if (lastSurahNumber != -1) {
          textSpans.add(const TextSpan(text: '\n\n'));
        }
        textSpans.add(
          TextSpan(
            text: 'سورة ${_getSurahName(verse.chapter)}\n',
            style: GoogleFonts.amiri(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade800),
          ),
        );
        if (verse.verse == 1 && verse.chapter != 9 && verse.chapter != 1) {
          textSpans.add(TextSpan(
            text: 'بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ\n',
            style: GoogleFonts.amiri(
                fontSize: 22, fontWeight: FontWeight.bold, color: Colors.black),
          ));
        }
        lastSurahNumber = verse.chapter;
      }
      textSpans.add(TextSpan(
        text: verse.text,
        style: GoogleFonts.amiri(
            fontSize: 24,
            height: 2.2,
            color: Colors.black87,
            fontWeight: FontWeight.w500),
      ));
      textSpans.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: GestureDetector(
          onTap: () => _playVerse(verse),
          child: Container(
            width: 30,
            height: 30,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.green.shade700, width: 1.5),
            ),
            child: Center(
              child: Text(
                _getArabicNumber(verse.verse),
                style: GoogleFonts.amiri(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade800),
              ),
            ),
          ),
        ),
      ));
    }
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(widget.juz.nameAr,
                      style: GoogleFonts.amiri(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800)),
                  Text('صفحة ${_getArabicNumber(_currentPage + 1)}',
                      style: GoogleFonts.amiri(
                          fontSize: 16, color: Colors.grey.shade600))
                ],
              ),
            ),
            const SizedBox(height: 16),
            Directionality(
              textDirection: TextDirection.rtl,
              child: RichText(
                textAlign: TextAlign.justify,
                text: TextSpan(children: textSpans),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            '${widget.juz.nameAr} - صفحة ${_getArabicNumber(_currentPage + 1)}',
            style: GoogleFonts.amiri(
                fontSize: 22, fontWeight: FontWeight.bold, color: Colors.white),
          ),
          centerTitle: true,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade600, Colors.green.shade800],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          elevation: 12,
          shadowColor: Colors.green.withOpacity(0.7),
          actions: [
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: _isPlaying
                      ? Colors.orange.shade600
                      : Colors.green.shade600,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                ),
              ),
              onPressed: _togglePlayPause,
            ),
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.red.shade600,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.stop, color: Colors.white),
              ),
              onPressed: _stopPlayback,
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : PageView.builder(
                controller: _pageController,
                itemCount: _quranPages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return _buildQuranPage(index);
                },
              ),
      ),
    );
  }

  static const Map<String, Map<String, String>> _quranReaders = {
    'ماهر المعيقلي': {
      'code': 'MaherAlMuaiqly128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕌'
    },
    'عبد الباسط عبد الصمد': {
      'code': 'Abdul_Basit_Murattal_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'سيد القراء',
      'icon': '📿'
    },
    'مشاري العفاسي': {
      'code': 'Alafasy_128kbps',
      'country': '🇰🇼 الكويت',
      'description': 'القارئ المبدع',
      'icon': '🎙️'
    },
    'سعد الغامدي': {
      'code': 'Ghamadi_40kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕋'
    },
    'أحمد العجمي': {
      'code': 'ahmed_ibn_ali_al_ajamy_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المتميز',
      'icon': '✨'
    },
    'محمود خليل الحصري': {
      'code': 'Husary_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'شيخ المقرئين',
      'icon': '👑'
    },
    'محمد أيوب': {
      'code': 'Muhammad_Ayyoub_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🌙'
    },
    'عبد الرحمن السديس': {
      'code': 'Abdurrahmaan_As-Sudais_192kbps',
      'country': '🇸🇦 السعودية',
      'description': 'رئيس الحرمين الشريفين',
      'icon': '🏛️'
    },
    'أبو بكر الشاطري': {
      'code': 'Abu_Bakr_Ash-Shaatree_128kbps',
      'country': '🇾🇪 اليمن',
      'description': 'القارئ الشاطري',
      'icon': '🎵'
    },
    'علي الحذيفي': {
      'code': 'Hudhaify_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم النبوي',
      'icon': '🌟'
    },
    'عبدالله عواد الجهني': {
      'code': 'Abdullaah_3awwaad_Al-Juhaynee_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المجيد',
      'icon': '🎭'
    },
    'علي جابر': {
      'code': 'Ali_Jaber_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المحترف',
      'icon': '🎪'
    },
    'فارس عباد': {
      'code': 'Fares_Abbad_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'صوت مميز',
      'icon': '🎨'
    },
    'ناصر القطامي': {
      'code': 'Nasser_Alqatami_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الشاب',
      'icon': '🌺'
    },
    'ياسر الدوسري': {
      'code': 'Yasser_Ad-Dussary_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'الصوت الذهبي',
      'icon': '🏆'
    },
    'سعود الشريم': {
      'code': 'Saood_ash-Shuraym_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '💎'
    },
    'محمد المحيسني': {
      'code': 'Muhammad_al_Mohisni_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الشاب المبدع',
      'icon': '🌸'
    },
    'خالد الجليل': {
      'code': 'Khalid_Al-Jaleel_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🌻'
    },
    'بندر بليلة': {
      'code': 'Bandar_Baleela_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🎯'
    },
    'صلاح البدير': {
      'code': 'Salah_Al_Budair_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🔮'
    },
    'عبد الله بصفر': {
      'code': 'Abdullah_Basfar_192kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المتقن',
      'icon': '🎁'
    },
    'محمد البراك': {
      'code': 'Mohammad_al_Barrak_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'صوت مؤثر',
      'icon': '🎻'
    },
    'عماد زهير حافظ': {
      'code': 'Emad_Zuhair_Hafez_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الحافظ',
      'icon': '📖'
    },
    'محمد صديق المنشاوي': {
      'code': 'Mohammad_Siddeeq_AlMinshawi_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'صاحب الصوت الذهبي',
      'icon': '🏆'
    },
    'محمد رفعت': {
      'code': 'Mohammed_Refaat_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الحرمين',
      'icon': '🎭'
    },
    'أحمد العزب': {
      'code': 'Ahmed_Al_Azab_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المبدع',
      'icon': '🎆'
    },
    'علي حجاج السويسي': {
      'code': 'Ali_Hajjaj_Al_Suesy_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'من علماء الأزهر',
      'icon': '🎓'
    },
    'محمود علي البنا': {
      'code': 'Mahmoud_Ali_Al_Banna_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الإذاعة المصرية',
      'icon': '📻'
    },
    'أبو العينين شعيشع': {
      'code': 'Abu_AlAynayn_Shoesha_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الأزهر الشريف',
      'icon': '🕌'
    },
    'عبد الفتاح الطروبي': {
      'code': 'Abdul_Fattah_At_Tarouti_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ معتمد',
      'icon': '🌿'
    },
    'محمد عبد الحكيم': {
      'code': 'Mohammad_Abdul_Hakeem_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ القاهرة',
      'icon': '🏦'
    },
    'محمود خليل الحصري - رواية ورش': {
      'code': 'Husary_Warsh_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'رواية ورش عن نافع',
      'icon': '📜'
    },
    'هاني الرفاعي': {
      'code': 'Hani_Rifai_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المصري المعروف',
      'icon': '🇪🇬'
    },
  };
}