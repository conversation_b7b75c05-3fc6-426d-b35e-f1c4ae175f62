import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:hijri/hijri_calendar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Initializer {
  static bool _initialized = false;
  static late SharedPreferences prefs;

  static Future<void> init() async {
    if (!_initialized) {
      try {
        WidgetsFlutterBinding.ensureInitialized();
        await initializeDateFormatting('ar', null);
        Intl.defaultLocale = 'ar';
        HijriCalendar.setLocal('ar');
        prefs = await SharedPreferences.getInstance();
        _initialized = true;
        debugPrint('Initialization completed successfully');
      } catch (e) {
        debugPrint('Error during initialization: $e');
        rethrow;
      }
    }
  }
}
