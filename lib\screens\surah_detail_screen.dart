import 'dart:async';
import 'dart:io' show TimeoutException;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:azkar_moslim/services/quran_service.dart';
import 'package:azkar_moslim/data/surah_names.dart';

class SurahDetailScreen extends StatefulWidget {
  final int surahNumber;
  final String surahName;

  const SurahDetailScreen({
    Key? key,
    required this.surahNumber,
    required this.surahName,
  }) : super(key: key);

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  late PageController _pageController;
  bool _isPlaying = false;
  bool _isLoading = false;
  bool _isPaused = false;
  int _currentVerse = 1;
  int _lastReadSurah = 1;
  int _lastReadVerse = 1;
  int _manualProgressSurah = 1;
  int _manualProgressVerse = 1;
  bool _showTraditionalVerseNumbers = true;
  Set<String> _bookmarkedVerses = <String>{};

  String _selectedReader = 'ماهر المعيقلي';

  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<void>? _playerCompleteSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;

  List<List<Map<String, dynamic>>> _quranPages = [];
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeAudioPlayer();
    _loadPreferences();
    _loadQuranPages();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // No background service dependency
  }

  void _initializeAudioPlayer() async {
    await _audioPlayer.setAudioContext(
      AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: {AVAudioSessionOptions.mixWithOthers},
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: false,
          stayAwake: true,
          contentType: AndroidContentType.music,
          usageType: AndroidUsageType.media,
          audioFocus: AndroidAudioFocus.gain,
        ),
      ),
    );
    _audioPlayer.setReleaseMode(ReleaseMode.stop);
    await _audioPlayer.setVolume(1.0);

    _playerStateSubscription =
        _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
          _isPaused = state == PlayerState.paused;
        });
      }
    });

    _playerCompleteSubscription = _audioPlayer.onPlayerComplete.listen((event) {
      if (mounted) {
        _playNextVerse();
      }
    });

    _durationSubscription = _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() {
          _duration = duration;
        });
      }
    });

    _positionSubscription = _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });
  }

  String _getArabicNumber(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      return arabicNumbers[int.parse(digit)];
    }).join();
  }

  Widget _buildVerseNumber(int verseNumber) {
    if (_showTraditionalVerseNumbers) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.shade300, width: 1),
        ),
        child: Text(
          '۝${_getArabicNumber(verseNumber)}۝',
          style: GoogleFonts.amiri(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.green.shade700,
          ),
        ),
      );
    } else {
      return Container(
        width: 38,
        height: 38,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [Colors.green.shade500, Colors.green.shade700],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.green.withOpacity(0.4),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: Colors.white,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            _getArabicNumber(verseNumber),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      );
    }
  }

  void _toggleBookmark(int surahNumber, int verseNumber) async {
    final key = '${surahNumber}_$verseNumber';
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      if (_bookmarkedVerses.contains(key)) {
        _bookmarkedVerses.remove(key);
      } else {
        _bookmarkedVerses.add(key);
      }
    });

    await prefs.setStringList('bookmarked_verses', _bookmarkedVerses.toList());

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _bookmarkedVerses.contains(key)
                ? 'تم حفظ الآية في العلامات المرجعية'
                : 'تم إزالة الآية من العلامات المرجعية',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor:
              _bookmarkedVerses.contains(key) ? Colors.green : Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }

  Future<void> _markReadingProgress(int surahNumber, int verseNumber) async {
    setState(() {
      _manualProgressSurah = surahNumber;
      _manualProgressVerse = verseNumber;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('manual_progress_surah', surahNumber);
    await prefs.setInt('manual_progress_verse', verseNumber);

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حفظ تقدمك في سورة ${_getSurahName(surahNumber)} - الآية ${_getArabicNumber(verseNumber)}',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _clearReadingProgress() async {
    setState(() {
      _manualProgressSurah = widget.surahNumber;
      _manualProgressVerse = 1;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('manual_progress_surah');
    await prefs.remove('manual_progress_verse');

    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            'تم مسح تقدم القراءة',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_reader', _selectedReader);
    await prefs.setInt('last_read_surah', _lastReadSurah);
    await prefs.setInt('last_read_verse', _lastReadVerse);
    await prefs.setBool(
      'show_traditional_verse_numbers',
      _showTraditionalVerseNumbers,
    );
    await prefs.setStringList('bookmarked_verses', _bookmarkedVerses.toList());
  }

  Future<void> _playVerse(int surahNumber, int verseNumber) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _currentVerse = verseNumber;
    });

    await _savePreferences();

    try {
      await _playVerseLocally(surahNumber, verseNumber);
    } catch (e) {
      _showErrorSnackBar(
        'تعذر تشغيل التلاوة. تحقق من الاتصال أو جرّب قارئاً آخر.',
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _playVerseLocally(int surahNumber, int verseNumber) async {
    try {
      await _audioPlayer.stop();

      final s3 = surahNumber.toString().padLeft(3, '0');
      final v3 = verseNumber.toString().padLeft(3, '0');
      final readerCode =
          _quranReaders[_selectedReader]?['code'] ?? 'MaherAlMuaiqly128kbps';
      final url = 'https://everyayah.com/data/$readerCode/${s3}${v3}.mp3';

      await _audioPlayer.play(UrlSource(url)).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException(
              'Audio playback timed out', const Duration(seconds: 10));
        },
      );
      if (mounted) {
        setState(() {
          _isPlaying = true;
          _isPaused = false;
        });
      }
    } catch (e) {
      debugPrint('Error playing verse locally: $e');
      if (mounted) {
        setState(() {
          _isPlaying = false;
          _isPaused = false;
        });
      }
      rethrow;
    }
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedReader = prefs.getString('selected_reader') ?? 'ماهر المعيقلي';
      _lastReadSurah = prefs.getInt('last_read_surah') ?? widget.surahNumber;
      _lastReadVerse = prefs.getInt('last_read_verse') ?? 1;
      _manualProgressSurah =
          prefs.getInt('manual_progress_surah') ?? widget.surahNumber;
      _manualProgressVerse = prefs.getInt('manual_progress_verse') ?? 1;
      _showTraditionalVerseNumbers =
          prefs.getBool('show_traditional_verse_numbers') ?? true;
      _bookmarkedVerses = Set<String>.from(
        prefs.getStringList('bookmarked_verses') ?? [],
      );
      _currentVerse = _lastReadVerse;
    });
  }

  void _playNextVerse() {
    _playNextVerseLocally();
  }

  void _playNextVerseLocally() {
    final quranService = Provider.of<QuranService>(context, listen: false);
    final ayahs = quranService.getSurahAyahs(widget.surahNumber);
    if (_currentVerse < ayahs.length) {
      _playVerse(widget.surahNumber, _currentVerse + 1);
    } else {
      if (widget.surahNumber < 114) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SurahDetailScreen(
              surahNumber: widget.surahNumber + 1,
              surahName: _getSurahName(widget.surahNumber + 1),
            ),
          ),
        );
      } else {
        setState(() {
          _isPlaying = false;
        });
      }
    }
  }

  void _playPreviousVerse() {
    _playPreviousVerseLocally();
  }

  void _playPreviousVerseLocally() {
    if (_currentVerse > 1) {
      _playVerse(widget.surahNumber, _currentVerse - 1);
    } else {
      if (widget.surahNumber > 1) {
        final prevSurahNumber = widget.surahNumber - 1;
        final prevSurahName = _getSurahName(prevSurahNumber);

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SurahDetailScreen(
              surahNumber: prevSurahNumber,
              surahName: prevSurahName,
            ),
          ),
        );
      }
    }
  }

  Future<void> _stopPlayback() async {
    await _stopPlaybackLocally();
  }

  Future<void> _stopPlaybackLocally() async {
    await _audioPlayer.stop();
    if (mounted) {
      setState(() {
        _isPlaying = false;
        _isPaused = false;
        _position = Duration.zero;
      });
    }
  }

  Future<void> _togglePlayPause() async {
    await _togglePlayPauseLocally();
  }

  Future<void> _togglePlayPauseLocally() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
      if (mounted) {
        setState(() {
          _isPaused = true;
        });
      }
    } else {
      if (_isPaused) {
        await _audioPlayer.resume();
        if (mounted) {
          setState(() {
            _isPaused = false;
          });
        }
      } else {
        _playVerse(widget.surahNumber, _currentVerse);
      }
    }
  }

  String _getSurahName(int surahNumber) {
    if (surahNumber >= 1 && surahNumber <= 114) {
      return surahNames[surahNumber - 1];
    }
    return '';
  }

  static const Map<String, Map<String, String>> _quranReaders = {
    'ماهر المعيقلي': {
      'code': 'MaherAlMuaiqly128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕌',
    },
    'عبد الباسط عبد الصمد': {
      'code': 'Abdul_Basit_Murattal_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'سيد القراء',
      'icon': '📿',
    },
    'مشاري العفاسي': {
      'code': 'Alafasy_128kbps',
      'country': '🇰🇼 الكويت',
      'description': 'القارئ المبدع',
      'icon': '🎙️',
    },
    'سعد الغامدي': {
      'code': 'Ghamadi_40kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🕋',
    },
    'أحمد العجمي': {
      'code': 'ahmed_ibn_ali_al_ajamy_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المتميز',
      'icon': '✨',
    },
    'محمود خليل الحصري': {
      'code': 'Husary_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'شيخ المقرئين',
      'icon': '👑',
    },
    'محمد أيوب': {
      'code': 'Muhammad_Ayyoub_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🌙',
    },
    'عبد الرحمن السديس': {
      'code': 'Abdurrahmaan_As-Sudais_192kbps',
      'country': '🇸🇦 السعودية',
      'description': 'رئيس الحرمين الشريفين',
      'icon': '🏛️',
    },
    'أبو بكر الشاطري': {
      'code': 'Abu_Bakr_Ash-Shaatree_128kbps',
      'country': '🇾🇪 اليمن',
      'description': 'القارئ الشاطري',
      'icon': '🎵',
    },
    'علي الحذيفي': {
      'code': 'Hudhaify_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم النبوي',
      'icon': '🌟',
    },
    'عبدالله عواد الجهني': {
      'code': 'Abdullaah_3awwaad_Al-Juhaynee_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المجيد',
      'icon': '🎭',
    },
    'علي جابر': {
      'code': 'Ali_Jaber_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المحترف',
      'icon': '🎪',
    },
    'فارس عباد': {
      'code': 'Fares_Abbad_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'صوت مميز',
      'icon': '🎨',
    },
    'ناصر القطامي': {
      'code': 'Nasser_Alqatami_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الشاب',
      'icon': '🌺',
    },
    'ياسر الدوسري': {
      'code': 'Yasser_Ad-Dussary_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'الصوت الذهبي',
      'icon': '🏆',
    },
    'سعود الشريم': {
      'code': 'Saood_ash-Shuraym_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '💎',
    },
    'محمد المحيسني': {
      'code': 'Muhammad_al_Mohisni_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الشاب المبدع',
      'icon': '🌸',
    },
    'خالد الجليل': {
      'code': 'Khalid_Al-Jaleel_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المكي',
      'icon': '🌻',
    },
    'بندر بليلة': {
      'code': 'Bandar_Baleela_64kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🎯',
    },
    'صلاح البدير': {
      'code': 'Salah_Al_Budair_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'إمام الحرم المدني',
      'icon': '🔮',
    },
    'عبد الله بصفر': {
      'code': 'Abdullah_Basfar_192kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ المتقن',
      'icon': '🎁',
    },
    'محمد البراك': {
      'code': 'Mohammad_al_Barrak_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'صوت مؤثر',
      'icon': '🎻',
    },
    'عماد زهير حافظ': {
      'code': 'Emad_Zuhair_Hafez_128kbps',
      'country': '🇸🇦 السعودية',
      'description': 'القارئ الحافظ',
      'icon': '📖',
    },
    'محمد صديق المنشاوي': {
      'code': 'Mohammad_Siddeeq_AlMinshawi_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'صاحب الصوت الذهبي',
      'icon': '🏆',
    },
    'محمد رفعت': {
      'code': 'Mohammed_Refaat_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الحرمين',
      'icon': '🎭',
    },
    'أحمد العزب': {
      'code': 'Ahmed_Al_Azab_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المبدع',
      'icon': '🎆',
    },
    'علي حجاج السويسي': {
      'code': 'Ali_Hajjaj_Al_Suesy_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'من علماء الأزهر',
      'icon': '🎓',
    },
    'محمود علي البنا': {
      'code': 'Mahmoud_Ali_Al_Banna_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الإذاعة المصرية',
      'icon': '📻',
    },
    'أبو العينين شعيشع': {
      'code': 'Abu_AlAynayn_Shoesha_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ الأزهر الشريف',
      'icon': '🕌',
    },
    'عبد الفتاح الطروبي': {
      'code': 'Abdul_Fattah_At_Tarouti_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ معتمد',
      'icon': '🌿',
    },
    'محمد عبد الحكيم': {
      'code': 'Mohammad_Abdul_Hakeem_128kbps',
      'country': '🇪🇬 مصر',
      'description': 'قارئ القاهرة',
      'icon': '🏦',
    },
    'محمود خليل الحصري - رواية ورش': {
      'code': 'Husary_Warsh_64kbps',
      'country': '🇪🇬 مصر',
      'description': 'رواية ورش عن نافع',
      'icon': '📜',
    },
    'هاني الرفاعي': {
      'code': 'Hani_Rifai_192kbps',
      'country': '🇪🇬 مصر',
      'description': 'القارئ المصري المعروف',
      'icon': '🇪🇬',
    },
  };

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _playerCompleteSubscription?.cancel();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _audioPlayer.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _loadQuranPages() {
    final quranService = Provider.of<QuranService>(context, listen: false);
    final ayahs = quranService.getSurahAyahs(widget.surahNumber);

    const versesPerPage = 10;
    List<List<Map<String, dynamic>>> pages = [];
    for (int i = 0; i < ayahs.length; i += versesPerPage) {
      final end =
          (i + versesPerPage < ayahs.length) ? i + versesPerPage : ayahs.length;
      pages.add(ayahs
          .sublist(i, end)
          .map((e) => {
                'text': e.text,
                'verse': e.verse,
              })
          .toList());
    }

    setState(() {
      _quranPages = pages;
      if (_lastReadVerse > 0 && ayahs.isNotEmpty) {
        _currentPage = ((_lastReadVerse - 1) / versesPerPage).floor();
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients) {
            _pageController.jumpToPage(_currentPage);
          }
        });
      }
    });
  }

  Widget _buildQuranPage(int pageIndex) {
    if (pageIndex < 0 || pageIndex >= _quranPages.length) {
      return const Center(child: Text('Page not found'));
    }

    final pageVerses = _quranPages[pageIndex];
    if (pageVerses.isEmpty) {
      return const Center(child: Text('No verses on this page'));
    }

    final List<InlineSpan> textSpans = [];

    final firstVerseOnPage = pageVerses.first;
    final surahNumber = widget.surahNumber;
    final verseNumber = firstVerseOnPage['verse'] as int;

    if (verseNumber == 1 && surahNumber != 9 && surahNumber != 1) {
      textSpans.add(TextSpan(
        text: 'بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ\n\n',
        style: GoogleFonts.amiri(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ));
    }

    for (int i = 0; i < pageVerses.length; i++) {
      final verse = pageVerses[i];
      textSpans.add(TextSpan(
        text: verse['text'] as String,
        style: GoogleFonts.amiri(
          fontSize: 24,
          height: 2.2,
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ));
      textSpans.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Container(
          width: 30,
          height: 30,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.green.shade700, width: 1.5),
          ),
          child: Center(
            child: Text(
              _getArabicNumber(verse['verse'] as int),
              style: GoogleFonts.amiri(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade800,
              ),
            ),
          ),
        ),
      ));
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'سورة ${widget.surahName}',
                    style: GoogleFonts.amiri(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade800,
                    ),
                  ),
                  Text(
                    'صفحة ${_getArabicNumber(_currentPage + 1)}',
                    style: GoogleFonts.amiri(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Directionality(
              textDirection: TextDirection.rtl,
              child: RichText(
                textAlign: TextAlign.justify,
                text: TextSpan(
                  children: textSpans,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final quranService = Provider.of<QuranService>(context);
    final ayahs = quranService.getSurahAyahs(widget.surahNumber);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.surahName} - صفحة ${_getArabicNumber(_currentPage + 1)}',
          style: GoogleFonts.amiri(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        elevation: 12,
        shadowColor: Colors.green.withOpacity(0.7),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _quranPages.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    return _buildQuranPage(index);
                  },
                ),
              ),
            ],
          ),
          if (_isPlaying || _isPaused)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Material(
                elevation: 8,
                color: Colors.green.shade700,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8.0, vertical: 4.0),
                  height: 60,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'سورة ${widget.surahName} - صفحة ${_currentPage + 1}',
                          style: GoogleFonts.amiri(
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.end,
                          textDirection: TextDirection.rtl,
                        ),
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.shade800,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.stop),
                              iconSize: 24,
                              color: Colors.white,
                              onPressed: _stopPlayback,
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.skip_next),
                              iconSize: 24,
                              color: Colors.white,
                              onPressed: () => _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeIn,
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.shade600,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(
                                  _isPlaying ? Icons.pause : Icons.play_arrow),
                              iconSize: 24,
                              color: Colors.white,
                              onPressed: () {
                                if (!_isLoading) {
                                  _togglePlayPause();
                                }
                              },
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.skip_previous),
                              iconSize: 24,
                              color: Colors.white,
                              onPressed: () => _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeIn,
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.shade800,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.stop_circle),
                              iconSize: 24,
                              color: Colors.white,
                              onPressed: _stopPlayback,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
